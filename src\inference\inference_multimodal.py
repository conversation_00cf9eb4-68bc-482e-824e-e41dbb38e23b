import os

# 添加src目录到Python路径
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import torch
import torch.nn as nn
import numpy as np
import cv2
import argparse
import yaml
from PIL import Image, ImageDraw, ImageFont
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from pathlib import Path
import albumentations as A
from albumentations.pytorch import ToTensorV2
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from src.models.multimodal_yolo_fixed import FixedMultimodalYOLO as SimpleMultimodalYOLO
from src.training.train_multimodal import load_config


class MultimodalInference:
    """多模态模型推理器"""
    
    def __init__(self, model_path, config=None):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        # 加载类别信息
        with open('configs/data.yaml', 'r', encoding='utf-8') as f:
            data_config = yaml.safe_load(f)
        self.class_names = data_config['names']
        self.nc = len(self.class_names)
        
        # 类别颜色
        self.colors = self._generate_colors()
        
        # 配置参数
        self.config = config or {}
        self.img_size = self.config.get('img_size', 640)
        
        # 数据预处理
        self.transform = A.Compose([
            A.Resize(self.img_size, self.img_size),
            A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            ToTensorV2()
        ])
        
        self.thermal_transform = A.Compose([
            A.Resize(self.img_size, self.img_size),
            A.Normalize(mean=[0.5], std=[0.5]),
            ToTensorV2()
        ])
        
        # 加载模型
        self.model = self._load_model(model_path)
    
    def _generate_colors(self):
        """生成类别颜色"""
        np.random.seed(42)
        colors = []
        for i in range(self.nc):
            color = tuple(np.random.randint(0, 255, 3).tolist())
            colors.append(color)
        return colors
    
    def _load_model(self, model_path):
        """加载训练好的模型"""
        print(f"加载模型: {model_path}")
        
        # 创建模型
        model = SimpleMultimodalYOLO(
            nc=self.nc,
            fusion_type=self.config.get('fusion_type', 'cross_attention')
        )
        
        # 加载权重
        checkpoint = torch.load(model_path, map_location=self.device)
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint)
        
        model = model.to(self.device)
        model.eval()
        
        print("模型加载完成")
        return model
    
    def preprocess_image(self, rgb_path, thermal_path=None):
        """预处理RGB和热红外图像"""
        # 读取RGB图像
        rgb_image = cv2.imread(rgb_path)
        if rgb_image is None:
            raise ValueError(f"无法读取RGB图像: {rgb_path}")
        
        rgb_image = cv2.cvtColor(rgb_image, cv2.COLOR_BGR2RGB)
        original_shape = rgb_image.shape[:2]
        
        # 读取热红外图像
        if thermal_path and os.path.exists(thermal_path):
            thermal_image = cv2.imread(thermal_path, cv2.IMREAD_GRAYSCALE)
        else:
            # 如果没有热红外图像，创建零填充图像
            thermal_image = np.zeros(original_shape, dtype=np.uint8)
            if thermal_path:
                print(f"警告: 热红外图像不存在 {thermal_path}，使用零填充")
        
        # 应用预处理
        rgb_transformed = self.transform(image=rgb_image)['image']
        thermal_transformed = self.thermal_transform(image=thermal_image)['image']
        
        # 添加batch维度
        rgb_tensor = rgb_transformed.unsqueeze(0).to(self.device)
        thermal_tensor = thermal_transformed.unsqueeze(0).to(self.device)
        
        return rgb_tensor, thermal_tensor, original_shape
    
    def postprocess_outputs(self, outputs, original_shape, conf_thresh=0.25, iou_thresh=0.45):
        """后处理模型输出"""
        detections = []
        
        for output in outputs:
            if not isinstance(output, torch.Tensor):
                continue
            
            # 假设输出格式为 [batch, num_anchors, num_classes + 5]
            pred = output[0]  # 取第一个（也是唯一一个）batch
            
            # 提取置信度和类别概率
            obj_conf = torch.sigmoid(pred[..., 4])  # 对象置信度
            cls_conf = torch.sigmoid(pred[..., 5:])  # 类别置信度
            
            # 计算最终置信度
            max_cls_conf, cls_pred = torch.max(cls_conf, dim=-1)
            final_conf = obj_conf * max_cls_conf
            
            # 置信度过滤
            valid_mask = final_conf > conf_thresh
            if not valid_mask.any():
                continue
            
            # 提取有效的预测
            valid_pred = pred[valid_mask]
            valid_conf = final_conf[valid_mask]
            valid_cls = cls_pred[valid_mask]
            
            # 转换边界框格式并缩放到原始图像尺寸
            boxes = valid_pred[:, :4].clone()
            boxes[:, 0] = (valid_pred[:, 0] - valid_pred[:, 2] / 2) * original_shape[1]  # x1
            boxes[:, 1] = (valid_pred[:, 1] - valid_pred[:, 3] / 2) * original_shape[0]  # y1
            boxes[:, 2] = (valid_pred[:, 0] + valid_pred[:, 2] / 2) * original_shape[1]  # x2
            boxes[:, 3] = (valid_pred[:, 1] + valid_pred[:, 3] / 2) * original_shape[0]  # y2
            
            # 组合结果
            for i in range(len(valid_conf)):
                detection = {
                    'bbox': boxes[i].cpu().numpy(),
                    'confidence': valid_conf[i].cpu().item(),
                    'class_id': valid_cls[i].cpu().item(),
                    'class_name': self.class_names[valid_cls[i].cpu().item()]
                }
                detections.append(detection)
        
        # 应用NMS
        detections = self._apply_nms(detections, iou_thresh)
        
        return detections
    
    def _apply_nms(self, detections, iou_thresh):
        """应用非极大值抑制"""
        if len(detections) == 0:
            return detections
        
        # 按置信度排序
        detections = sorted(detections, key=lambda x: x['confidence'], reverse=True)
        
        keep = []
        while detections:
            # 保留置信度最高的检测
            current = detections.pop(0)
            keep.append(current)
            
            # 移除与当前检测IoU过高的其他检测
            remaining = []
            for det in detections:
                if self._compute_iou(current['bbox'], det['bbox']) < iou_thresh:
                    remaining.append(det)
            detections = remaining
        
        return keep
    
    def _compute_iou(self, box1, box2):
        """计算两个边界框的IoU"""
        x1_inter = max(box1[0], box2[0])
        y1_inter = max(box1[1], box2[1])
        x2_inter = min(box1[2], box2[2])
        y2_inter = min(box1[3], box2[3])
        
        if x2_inter <= x1_inter or y2_inter <= y1_inter:
            return 0.0
        
        inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)
        
        area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
        area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
        
        union_area = area1 + area2 - inter_area
        
        return inter_area / union_area if union_area > 0 else 0.0
    
    def predict(self, rgb_path, thermal_path=None, conf_thresh=0.25, iou_thresh=0.45):
        """对单张图像进行预测"""
        # 预处理
        rgb_tensor, thermal_tensor, original_shape = self.preprocess_image(rgb_path, thermal_path)
        
        # 推理
        with torch.no_grad():
            outputs = self.model(rgb_tensor, thermal_tensor)
        
        # 后处理
        detections = self.postprocess_outputs(outputs, original_shape, conf_thresh, iou_thresh)
        
        return detections
    
    def visualize_results(self, rgb_path, detections, save_path=None, show_conf=True):
        """可视化检测结果"""
        # 读取原始图像
        image = cv2.imread(rgb_path)
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 创建matplotlib图形
        fig, ax = plt.subplots(1, 1, figsize=(12, 8))
        ax.imshow(image)
        
        # 绘制检测框
        for det in detections:
            bbox = det['bbox']
            class_id = det['class_id']
            confidence = det['confidence']
            class_name = det['class_name']
            
            # 获取颜色
            color = [c/255.0 for c in self.colors[class_id]]
            
            # 绘制边界框
            rect = patches.Rectangle(
                (bbox[0], bbox[1]), 
                bbox[2] - bbox[0], 
                bbox[3] - bbox[1],
                linewidth=2, 
                edgecolor=color, 
                facecolor='none'
            )
            ax.add_patch(rect)
            
            # 添加标签
            if show_conf:
                label = f'{class_name}: {confidence:.2f}'
            else:
                label = class_name
            
            ax.text(
                bbox[0], bbox[1] - 5,
                label,
                fontsize=12,
                color=color,
                weight='bold',
                bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.8)
            )
        
        ax.set_title(f'检测结果 (共检测到 {len(detections)} 个目标)', fontsize=14, weight='bold')
        ax.axis('off')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"结果已保存到: {save_path}")
        
        plt.show()
        
        return fig
    
    def batch_predict(self, input_dir, output_dir, conf_thresh=0.25, iou_thresh=0.45):
        """批量预测"""
        input_path = Path(input_dir)
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 支持的图像格式
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
        
        # 查找所有RGB图像
        rgb_files = []
        for ext in image_extensions:
            rgb_files.extend(input_path.glob(f'*{ext}'))
            rgb_files.extend(input_path.glob(f'*{ext.upper()}'))
        
        print(f"找到 {len(rgb_files)} 张图像")
        
        results = []
        
        for rgb_file in rgb_files:
            print(f"处理: {rgb_file.name}")
            
            # 查找对应的热红外图像
            base_name = rgb_file.stem
            thermal_file = None
            
            # 常见的热红外图像命名模式
            thermal_patterns = [
                f"{base_name}_thermal.png",
                f"{base_name}_thermal.jpg",
                f"{base_name}_T.png",
                f"{base_name}_T.jpg"
            ]
            
            for pattern in thermal_patterns:
                thermal_path = input_path / pattern
                if thermal_path.exists():
                    thermal_file = thermal_path
                    break
            
            try:
                # 预测
                detections = self.predict(
                    str(rgb_file), 
                    str(thermal_file) if thermal_file else None,
                    conf_thresh, 
                    iou_thresh
                )
                
                # 可视化并保存
                output_file = output_path / f"{base_name}_result.jpg"
                self.visualize_results(
                    str(rgb_file), 
                    detections, 
                    str(output_file),
                    show_conf=True
                )
                plt.close()  # 关闭图形以释放内存
                
                # 保存检测结果到JSON
                result = {
                    'filename': rgb_file.name,
                    'thermal_file': thermal_file.name if thermal_file else None,
                    'detections': detections
                }
                results.append(result)
                
                print(f"  检测到 {len(detections)} 个目标")
                
            except Exception as e:
                print(f"  处理失败: {e}")
                continue
        
        # 保存所有结果
        import json
        results_file = output_path / 'detection_results.json'
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n批量预测完成！结果保存在: {output_dir}")
        print(f"检测结果JSON文件: {results_file}")
        
        return results
    
    def compare_modalities(self, rgb_path, thermal_path, conf_thresh=0.25, iou_thresh=0.45):
        """比较不同模态的检测结果"""
        # RGB单独检测
        rgb_tensor, _, original_shape = self.preprocess_image(rgb_path, None)
        thermal_zero = torch.zeros_like(rgb_tensor[:, :1])  # 零填充热红外
        
        with torch.no_grad():
            rgb_outputs = self.model(rgb_tensor, thermal_zero)
        rgb_detections = self.postprocess_outputs(rgb_outputs, original_shape, conf_thresh, iou_thresh)
        
        # 热红外单独检测（如果存在）
        thermal_detections = []
        if thermal_path and os.path.exists(thermal_path):
            rgb_zero = torch.zeros_like(rgb_tensor)  # 零填充RGB
            _, thermal_tensor, _ = self.preprocess_image(rgb_path, thermal_path)
            
            with torch.no_grad():
                thermal_outputs = self.model(rgb_zero, thermal_tensor)
            thermal_detections = self.postprocess_outputs(thermal_outputs, original_shape, conf_thresh, iou_thresh)
        
        # 多模态检测
        if thermal_path and os.path.exists(thermal_path):
            multimodal_detections = self.predict(rgb_path, thermal_path, conf_thresh, iou_thresh)
        else:
            multimodal_detections = rgb_detections
        
        # 可视化比较
        fig, axes = plt.subplots(1, 3, figsize=(18, 6))
        
        # RGB检测结果
        image = cv2.imread(rgb_path)
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        for i, (ax, detections, title) in enumerate(zip(
            axes, 
            [rgb_detections, thermal_detections, multimodal_detections],
            ['RGB单独检测', '热红外单独检测', '多模态融合检测']
        )):
            ax.imshow(image)
            
            for det in detections:
                bbox = det['bbox']
                class_id = det['class_id']
                confidence = det['confidence']
                class_name = det['class_name']
                
                color = [c/255.0 for c in self.colors[class_id]]
                
                rect = patches.Rectangle(
                    (bbox[0], bbox[1]), 
                    bbox[2] - bbox[0], 
                    bbox[3] - bbox[1],
                    linewidth=2, 
                    edgecolor=color, 
                    facecolor='none'
                )
                ax.add_patch(rect)
                
                ax.text(
                    bbox[0], bbox[1] - 5,
                    f'{class_name}: {confidence:.2f}',
                    fontsize=10,
                    color=color,
                    weight='bold',
                    bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.8)
                )
            
            ax.set_title(f'{title}\n(检测数量: {len(detections)})', fontsize=12, weight='bold')
            ax.axis('off')
        
        plt.tight_layout()
        plt.show()
        
        return {
            'rgb_only': rgb_detections,
            'thermal_only': thermal_detections,
            'multimodal': multimodal_detections
        }


def main():
    parser = argparse.ArgumentParser(description='多模态绝缘子检测模型推理')
    parser.add_argument('--model_path', type=str, required=True, help='模型权重路径')
    parser.add_argument('--config', type=str, help='配置文件路径')
    parser.add_argument('--rgb_image', type=str, help='RGB图像路径（单图预测）')
    parser.add_argument('--thermal_image', type=str, help='热红外图像路径（单图预测）')
    parser.add_argument('--input_dir', type=str, help='输入图像目录（批量预测）')
    parser.add_argument('--output_dir', type=str, default='runs/inference', help='输出目录')
    parser.add_argument('--conf_thresh', type=float, default=0.25, help='置信度阈值')
    parser.add_argument('--iou_thresh', type=float, default=0.45, help='IoU阈值')
    parser.add_argument('--compare_modalities', action='store_true', help='比较不同模态的检测结果')
    
    args = parser.parse_args()
    
    # 加载配置
    if args.config:
        config = load_config(args.config)
    else:
        config = {
            'img_size': 640,
            'fusion_type': 'cross_attention'
        }
    
    # 创建推理器
    inferencer = MultimodalInference(args.model_path, config)
    
    if args.rgb_image:
        # 单图预测
        print(f"处理单张图像: {args.rgb_image}")
        
        if args.compare_modalities:
            # 比较不同模态
            results = inferencer.compare_modalities(
                args.rgb_image, 
                args.thermal_image,
                args.conf_thresh,
                args.iou_thresh
            )
            
            print("\n=== 模态比较结果 ===")
            print(f"RGB单独检测: {len(results['rgb_only'])} 个目标")
            print(f"热红外单独检测: {len(results['thermal_only'])} 个目标")
            print(f"多模态融合检测: {len(results['multimodal'])} 个目标")
            
        else:
            # 常规预测
            detections = inferencer.predict(
                args.rgb_image, 
                args.thermal_image,
                args.conf_thresh,
                args.iou_thresh
            )
            
            print(f"检测到 {len(detections)} 个目标:")
            for det in detections:
                print(f"  {det['class_name']}: {det['confidence']:.3f}")
            
            # 可视化
            os.makedirs(args.output_dir, exist_ok=True)
            output_file = os.path.join(args.output_dir, 'result.jpg')
            inferencer.visualize_results(args.rgb_image, detections, output_file)
    
    elif args.input_dir:
        # 批量预测
        print(f"批量处理目录: {args.input_dir}")
        results = inferencer.batch_predict(
            args.input_dir,
            args.output_dir,
            args.conf_thresh,
            args.iou_thresh
        )
        
        # 统计结果
        total_detections = sum(len(r['detections']) for r in results)
        print(f"\n=== 批量处理统计 ===")
        print(f"处理图像数量: {len(results)}")
        print(f"总检测目标数量: {total_detections}")
        print(f"平均每张图像检测目标: {total_detections/len(results):.1f}")
    
    else:
        print("请提供 --rgb_image 或 --input_dir 参数")


if __name__ == '__main__':
    main() 