import os

# 添加src目录到Python路径
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import torch
import numpy as np
from PIL import Image
import cv2
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
import albumentations as A
from albumentations.pytorch import ToTensorV2
import yaml


class MultimodalInsulatorDataset(Dataset):
    """多模态绝缘子检测数据集"""
    
    def __init__(self, data_dir, split='train', img_size=640, augment=True,
                 active_classes=None, curriculum_difficulty=False, stage=1):
        """
        Args:
            data_dir: 数据集根目录
            split: 数据分割 ('train', 'valid', 'test')
            img_size: 图像尺寸
            augment: 是否进行数据增强
            active_classes: 活跃类别列表，用于渐进式训练
            curriculum_difficulty: 是否启用课程学习
            stage: 当前训练阶段
        """
        self.data_dir = data_dir
        self.split = split
        self.img_size = img_size
        self.augment = augment and split == 'train'
        self.active_classes = active_classes or list(range(5))  # 默认所有类别
        self.curriculum_difficulty = curriculum_difficulty
        self.stage = stage
        
        # 设置路径
        self.rgb_dir = os.path.join(data_dir, split, 'images')
        self.thermal_dir = os.path.join(data_dir, split, 'images', 'thermal')
        self.labels_dir = os.path.join(data_dir, split, 'labels')
        
        # 获取所有RGB图像文件
        all_rgb_files = []
        for f in os.listdir(self.rgb_dir):
            if f.lower().endswith(('.jpg', '.jpeg', '.png')):
                all_rgb_files.append(f)

        # 根据活跃类别过滤数据
        self.rgb_files = self._filter_files_by_classes(all_rgb_files)

        # 如果启用课程学习，按难度排序
        if self.curriculum_difficulty and split == 'train':
            self.rgb_files = self._sort_by_difficulty(self.rgb_files)

        print(f"加载 {split} 数据集: {len(self.rgb_files)} 张图像 (活跃类别: {self.active_classes})")
        
        # 定义数据增强
        if self.augment:
            self.transform = A.Compose([
                A.Resize(img_size, img_size),
                A.HorizontalFlip(p=0.5),
                A.VerticalFlip(p=0.2),
                A.RandomBrightnessContrast(p=0.3),
                A.Blur(blur_limit=3, p=0.1),
                A.CLAHE(p=0.1),
                A.ColorJitter(p=0.2),
                A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
                ToTensorV2()
            ], bbox_params=A.BboxParams(format='yolo', label_fields=['class_labels']))
        else:
            self.transform = A.Compose([
                A.Resize(img_size, img_size),
                A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
                ToTensorV2()
            ], bbox_params=A.BboxParams(format='yolo', label_fields=['class_labels']))
        
        # 热红外图像的归一化参数（根据实际数据调整）
        self.thermal_transform = A.Compose([
            A.Resize(img_size, img_size),
            A.Normalize(mean=[0.5], std=[0.5]),  # 单通道热红外图像
            ToTensorV2()
        ])

    def _filter_files_by_classes(self, all_files):
        """根据活跃类别过滤文件"""
        if len(self.active_classes) == 5:  # 如果包含所有类别，不需要过滤
            return all_files

        filtered_files = []
        for filename in all_files:
            label_path = os.path.join(self.labels_dir,
                                    os.path.splitext(filename)[0] + '.txt')

            if os.path.exists(label_path):
                try:
                    with open(label_path, 'r') as f:
                        lines = f.readlines()

                    # 检查是否包含活跃类别
                    has_active_class = False
                    for line in lines:
                        if line.strip():
                            class_id = int(line.split()[0])
                            if class_id in self.active_classes:
                                has_active_class = True
                                break

                    if has_active_class:
                        filtered_files.append(filename)

                except Exception as e:
                    print(f"警告: 无法读取标签文件 {label_path}: {e}")
                    continue

        return filtered_files

    def _sort_by_difficulty(self, files):
        """按难度排序文件（课程学习）"""
        # 简单的难度评估：根据目标数量和类别复杂度
        file_difficulties = []

        for filename in files:
            label_path = os.path.join(self.labels_dir,
                                    os.path.splitext(filename)[0] + '.txt')
            difficulty = 0

            if os.path.exists(label_path):
                try:
                    with open(label_path, 'r') as f:
                        lines = f.readlines()

                    # 目标数量影响难度
                    num_objects = len([line for line in lines if line.strip()])
                    difficulty += num_objects * 0.3

                    # 少数类别增加难度
                    for line in lines:
                        if line.strip():
                            class_id = int(line.split()[0])
                            if class_id in [2, 4]:  # 少数类别
                                difficulty += 1.0

                except Exception:
                    difficulty = 0.5  # 默认中等难度

            file_difficulties.append((filename, difficulty))

        # 按难度排序（从简单到困难）
        file_difficulties.sort(key=lambda x: x[1])

        # 根据训练阶段选择难度范围
        total_files = len(file_difficulties)
        if self.stage == 1:
            # 第一阶段：使用前60%的简单样本
            end_idx = int(total_files * 0.6)
            selected_files = file_difficulties[:end_idx]
        elif self.stage == 2:
            # 第二阶段：使用前80%的样本
            end_idx = int(total_files * 0.8)
            selected_files = file_difficulties[:end_idx]
        else:
            # 第三阶段：使用所有样本
            selected_files = file_difficulties

        return [f[0] for f in selected_files]

    def __len__(self):
        return len(self.rgb_files)
    
    def __getitem__(self, idx):
        # 获取文件名
        rgb_filename = self.rgb_files[idx]
        
        # 生成对应的热红外图像文件名
        base_name = os.path.splitext(rgb_filename)[0]
        thermal_filename = f"{base_name}_thermal.png"
        
        # 生成标签文件名
        label_filename = f"{base_name}.txt"
        
        # 读取RGB图像
        rgb_path = os.path.join(self.rgb_dir, rgb_filename)
        rgb_image = cv2.imread(rgb_path)
        rgb_image = cv2.cvtColor(rgb_image, cv2.COLOR_BGR2RGB)
        
        # 读取热红外图像
        thermal_path = os.path.join(self.thermal_dir, thermal_filename)
        if os.path.exists(thermal_path):
            thermal_image = cv2.imread(thermal_path, cv2.IMREAD_GRAYSCALE)
        else:
            # 如果热红外图像不存在，创建零填充图像
            thermal_image = np.zeros((rgb_image.shape[0], rgb_image.shape[1]), dtype=np.uint8)
            print(f"警告: 热红外图像不存在 {thermal_path}")
        
        # 读取标签
        label_path = os.path.join(self.labels_dir, label_filename)
        bboxes = []
        class_labels = []
        
        if os.path.exists(label_path):
            with open(label_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f.readlines(), 1):
                    line = line.strip()
                    if line:
                        parts = line.split()
                        if len(parts) < 5:
                            print(f"警告: {label_path} 第{line_num}行格式错误，跳过")
                            continue
                            
                        class_id = int(parts[0])
                        x_center = float(parts[1])
                        y_center = float(parts[2])
                        width = float(parts[3])
                        height = float(parts[4])
                        
                        # 验证和修复边界框坐标
                        original_bbox = [x_center, y_center, width, height]
                        x_center = max(0.0, min(1.0, x_center))
                        y_center = max(0.0, min(1.0, y_center))
                        width = max(0.0, min(1.0, width))
                        height = max(0.0, min(1.0, height))
                        
                        # 检查修复后的bbox是否有效
                        if width <= 0 or height <= 0:
                            print(f"警告: {label_path} 第{line_num}行边界框无效，跳过: {original_bbox}")
                            continue
                        
                        # 确保bbox不会超出图像边界
                        x_min = x_center - width / 2
                        y_min = y_center - height / 2
                        x_max = x_center + width / 2
                        y_max = y_center + height / 2
                        
                        if x_min < 0:
                            x_center = width / 2
                        if y_min < 0:
                            y_center = height / 2
                        if x_max > 1:
                            x_center = 1 - width / 2
                        if y_max > 1:
                            y_center = 1 - height / 2
                        
                        # 只在调试模式下输出修复信息，减少噪音
                        debug_mode = os.environ.get('BBOX_DEBUG', 'false').lower() == 'true'
                        if debug_mode and original_bbox != [x_center, y_center, width, height]:
                            print(f"修复边界框: {label_path} 第{line_num}行")
                            print(f"  原始: {original_bbox}")
                            print(f"  修复: {[x_center, y_center, width, height]}")
                        
                        bboxes.append([x_center, y_center, width, height])
                        class_labels.append(class_id)
        
        # 应用数据增强（只对RGB图像）
        if len(bboxes) > 0:
            # 有标签的情况，正常处理
            if self.augment:
                transformed = self.transform(image=rgb_image, bboxes=bboxes, class_labels=class_labels)
                rgb_tensor = transformed['image']
                bboxes = transformed['bboxes']
                class_labels = transformed['class_labels']
            else:
                transformed = self.transform(image=rgb_image, bboxes=bboxes, class_labels=class_labels)
                rgb_tensor = transformed['image']
                bboxes = transformed['bboxes']
                class_labels = transformed['class_labels']
        else:
            # 没有标签的情况，只对图像进行变换，不处理边界框
            print(f"警告: {rgb_image_path} 没有有效标签，跳过边界框处理")
            # 使用不包含边界框处理的变换
            basic_transform = A.Compose([
                A.Resize(self.img_size, self.img_size),
                A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
                ToTensorV2()
            ])
            transformed = basic_transform(image=rgb_image)
            rgb_tensor = transformed['image']
            bboxes = []
            class_labels = []
        
        # 处理热红外图像（不进行数据增强以保持与RGB图像的对应）
        thermal_transformed = self.thermal_transform(image=thermal_image)
        thermal_tensor = thermal_transformed['image']
        
        # 转换标签格式
        if len(bboxes) > 0:
            targets = torch.zeros((len(bboxes), 6))  # [batch_id, class_id, x, y, w, h]
            for i, (bbox, class_id) in enumerate(zip(bboxes, class_labels)):
                targets[i] = torch.tensor([0, class_id, bbox[0], bbox[1], bbox[2], bbox[3]])
        else:
            # 没有标签时创建空的targets张量
            targets = torch.zeros((0, 6))
        
        return {
            'rgb': rgb_tensor,
            'thermal': thermal_tensor,
            'targets': targets,
            'filename': rgb_filename
        }


def multimodal_collate_fn(batch):
    """自定义批次整理函数 - 移到模块级别以支持Windows多进程"""
    rgb_images = torch.stack([item['rgb'] for item in batch])
    thermal_images = torch.stack([item['thermal'] for item in batch])
    
    # 处理targets，添加batch索引
    targets = []
    for i, item in enumerate(batch):
        if len(item['targets']) > 0:
            batch_targets = item['targets'].clone()
            batch_targets[:, 0] = i  # 设置batch索引
            targets.append(batch_targets)
    
    if targets:
        targets = torch.cat(targets, 0)
    else:
        targets = torch.zeros((0, 6))
    
    filenames = [item['filename'] for item in batch]
    
    return {
        'rgb': rgb_images,
        'thermal': thermal_images,
        'targets': targets,
        'filenames': filenames
    }


def create_dataloader(data_dir, split, batch_size=16, img_size=640, num_workers=4, shuffle=True):
    """创建数据加载器"""
    dataset = MultimodalInsulatorDataset(
        data_dir=data_dir,
        split=split,
        img_size=img_size,
        augment=(split == 'train')
    )
    
    # Windows下自动设置num_workers=0以避免多进程问题
    import platform
    if platform.system() == 'Windows' and num_workers > 0:
        print(f"⚠️ Windows系统检测到，自动设置 num_workers=0 (原值: {num_workers})")
        num_workers = 0
    
    dataloader = DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        num_workers=num_workers,
        collate_fn=multimodal_collate_fn,
        pin_memory=(num_workers > 0)  # Windows单进程模式下不使用pin_memory
    )
    
    return dataloader


if __name__ == "__main__":
    # 测试数据加载器
    data_dir = "."  # 当前目录
    
    # 创建训练数据加载器
    train_loader = create_dataloader(data_dir, 'train', batch_size=4, img_size=640)
    
    # 测试加载一个批次
    for batch in train_loader:
        print("RGB图像形状:", batch['rgb'].shape)
        print("热红外图像形状:", batch['thermal'].shape)
        print("目标形状:", batch['targets'].shape)
        print("文件名:", batch['filenames'])
        break 