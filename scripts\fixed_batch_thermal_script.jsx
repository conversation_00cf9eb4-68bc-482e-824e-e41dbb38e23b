// 修复版批量RGB转热红外灰度图Photoshop脚本
// 解决语法错误和非法参数问题

// 全局设置变量
var batchSettings = {
    inputFolder: null,
    outputFolder: null,
    processedCount: 0,
    failedCount: 0,
    applyPixelation: false,
    deviceType: "professional", // "professional" 或 "traditional"
    outputFormat: "jpg", // "jpg", "png", "tif"
    outputQuality: 10, // JPEG质量 1-12
    addSuffix: "_thermal", // 输出文件后缀
    logResults: true
};

function batchThermalConversion() {
    try {
        // 选择输入文件夹
        batchSettings.inputFolder = Folder.selectDialog("选择包含RGB图片的文件夹");
        if (!batchSettings.inputFolder) {
            alert("未选择输入文件夹，操作已取消。");
            return;
        }
        
        // 选择输出文件夹
        batchSettings.outputFolder = Folder.selectDialog("选择热成像图片的输出文件夹");
        if (!batchSettings.outputFolder) {
            alert("未选择输出文件夹，操作已取消。");
            return;
        }
        
        // 配置批量处理选项
        if (!configureBatchSettings()) {
            return;
        }
        
        // 获取所有支持的图片文件
        var imageFiles = getImageFiles(batchSettings.inputFolder);
        
        if (imageFiles.length === 0) {
            alert("在选择的文件夹中未找到支持的图片文件！\n支持格式：JPG, PNG, TIF, BMP, PSD");
            return;
        }
        
        // 确认开始批量处理
        var confirmMessage = "批量处理设置：\n\n" +
                            "输入文件夹：" + batchSettings.inputFolder.name + "\n" +
                            "输出文件夹：" + batchSettings.outputFolder.name + "\n" +
                            "找到图片：" + imageFiles.length + " 张\n" +
                            "设备模拟：" + (batchSettings.deviceType === "professional" ? "大疆H30T专业级" : "传统热成像仪") + "\n" +
                            "像素化效果：" + (batchSettings.applyPixelation ? "是" : "否") + "\n" +
                            "输出格式：" + batchSettings.outputFormat.toUpperCase() + "\n\n" +
                            "是否开始批量处理？";
        
        if (!confirm(confirmMessage)) {
            return;
        }
        
        // 开始批量处理
        processBatch(imageFiles);
        
    } catch (e) {
        alert("批量处理初始化失败：" + e.message);
    }
}

function configureBatchSettings() {
    try {
        // 设备类型选择
        var deviceChoice = confirm("选择热成像设备类型：\n\n点击'确定' = 大疆H30T专业级（1280×1024）\n点击'取消' = 传统热成像仪（320×240）");
        batchSettings.deviceType = deviceChoice ? "professional" : "traditional";
        
        // 像素化效果选择
        batchSettings.applyPixelation = confirm("是否应用像素化效果？\n\n建议：\n- 用于展示/对比：选择'确定'\n- 用于专业分析：选择'取消'");
        
        // 输出格式选择
        var formatChoice = prompt("选择输出格式（输入数字）：\n\n1 = JPG（推荐，文件小）\n2 = PNG（无损，文件大）\n3 = TIF（专业，文件最大）", "1");
        
        switch (formatChoice) {
            case "1":
                batchSettings.outputFormat = "jpg";
                // JPG质量设置
                var qualityInput = prompt("设置JPG质量（1-12，推荐10）：", "10");
                var quality = parseInt(qualityInput, 10);
                batchSettings.outputQuality = (quality >= 1 && quality <= 12) ? quality : 10;
                break;
            case "2":
                batchSettings.outputFormat = "png";
                break;
            case "3":
                batchSettings.outputFormat = "tif";
                break;
            default:
                batchSettings.outputFormat = "jpg";
                batchSettings.outputQuality = 10;
        }
        
        // 文件命名选择
        var suffix = prompt("输入输出文件后缀（如：_thermal、_H30T、_infrared）：", "_thermal");
        batchSettings.addSuffix = suffix || "_thermal";
        
        return true;
        
    } catch (e) {
        alert("配置设置时出错：" + e.message);
        return false;
    }
}

function getImageFiles(folder) {
    var files = folder.getFiles();
    var imageFiles = [];
    var supportedExtensions = [".jpg", ".jpeg", ".png", ".tif", ".tiff", ".bmp", ".psd"];
    
    for (var i = 0; i < files.length; i++) {
        if (files[i] instanceof File) {
            var fileName = files[i].name.toLowerCase();
            var isImageFile = false;
            
            for (var j = 0; j < supportedExtensions.length; j++) {
                if (fileName.lastIndexOf(supportedExtensions[j]) !== -1 && 
                    fileName.lastIndexOf(supportedExtensions[j]) === fileName.length - supportedExtensions[j].length) {
                    isImageFile = true;
                    break;
                }
            }
            
            if (isImageFile) {
                imageFiles.push(files[i]);
            }
        }
    }
    
    return imageFiles;
}

function processBatch(imageFiles) {
    var startTime = new Date();
    var logMessages = [];
    
    // 保存当前的标尺单位设置
    var originalRulerUnits = app.preferences.rulerUnits;
    
    try {
        app.preferences.rulerUnits = Units.PIXELS;
        
        logMessages.push("=== 批量热成像转换开始 ===");
        logMessages.push("开始时间：" + startTime);
        logMessages.push("设备类型：" + (batchSettings.deviceType === "professional" ? "大疆H30T" : "传统热成像仪"));
        logMessages.push("输出格式：" + batchSettings.outputFormat.toUpperCase());
        logMessages.push("------------------------");
        
        for (var i = 0; i < imageFiles.length; i++) {
            var currentFile = imageFiles[i];
            var progress = "处理进度：" + (i + 1) + "/" + imageFiles.length + " - " + currentFile.name;
            
            // 显示进度
            if (i % 5 === 0 || i === imageFiles.length - 1) {
                $.writeln(progress); // 使用控制台输出而不是弹窗
            }
            
            // 处理单个文件
            var result = processSingleFile(currentFile);
            
            if (result.success) {
                batchSettings.processedCount++;
                logMessages.push("✓ " + currentFile.name + " -> " + result.outputName);
            } else {
                batchSettings.failedCount++;
                logMessages.push("? " + currentFile.name + " - 失败：" + result.error);
            }
        }
        
    } catch (e) {
        logMessages.push("批量处理异常：" + e.message);
    } finally {
        // 恢复标尺单位设置
        app.preferences.rulerUnits = originalRulerUnits;
    }
    
    // 显示完成统计
    var endTime = new Date();
    var duration = Math.round((endTime - startTime) / 1000);
    
    logMessages.push("------------------------");
    logMessages.push("=== 批量处理完成 ===");
    logMessages.push("结束时间：" + endTime);
    logMessages.push("总耗时：" + duration + " 秒");
    logMessages.push("成功处理：" + batchSettings.processedCount + " 张");
    logMessages.push("处理失败：" + batchSettings.failedCount + " 张");
    
    // 显示结果
    var resultMessage = "批量处理完成！\n\n" +
                       "成功：" + batchSettings.processedCount + " 张\n" +
                       "失败：" + batchSettings.failedCount + " 张\n" +
                       "总耗时：" + duration + " 秒\n\n" +
                       "输出位置：" + batchSettings.outputFolder.fsName;
    
    alert(resultMessage);
    
    // 保存处理日志
    if (batchSettings.logResults) {
        saveProcessingLog(logMessages);
    }
}

function processSingleFile(file) {
    var result = {
        success: false,
        outputName: "",
        error: ""
    };
    
    var doc = null;
    
    try {
        // 检查文件是否存在
        if (!file.exists) {
            result.error = "文件不存在";
            return result;
        }
        
        // 打开文件
        doc = app.open(file);
        
        if (!doc) {
            result.error = "无法打开文件";
            return result;
        }
        
        // 检查文档模式
        if (doc.mode != DocumentMode.RGB && doc.mode != DocumentMode.GRAYSCALE && doc.mode != DocumentMode.CMYK) {
            result.error = "不支持的颜色模式：" + doc.mode;
            return result;
        }
        
        // 执行热成像转换
        performBatchThermalConversion(doc);
        
        // 生成输出文件名
        var baseName = file.name.replace(/\.[^\.]+$/, ""); // 移除扩展名
        var outputName = baseName + batchSettings.addSuffix + "." + batchSettings.outputFormat;
        var outputFile = new File(batchSettings.outputFolder + "/" + outputName);
        
        // 保存文件
        saveProcessedFile(doc, outputFile);
        
        result.success = true;
        result.outputName = outputName;
        
    } catch (e) {
        result.error = e.message;
    } finally {
        // 确保文档被关闭
        if (doc) {
            try {
                doc.close(SaveOptions.DONOTSAVECHANGES);
            } catch (closeError) {
                // 忽略关闭错误
            }
        }
    }
    
    return result;
}

function performBatchThermalConversion(doc) {
    try {
        // 解锁背景图层
        if (doc.backgroundLayer) {
            try {
                doc.backgroundLayer.isBackgroundLayer = false;
            } catch (e) {
                // 忽略解锁错误
            }
        }
        
        // 先转换到RGB模式（如果不是的话）
        if (doc.mode != DocumentMode.RGB) {
            doc.changeMode(ChangeMode.RGB);
        }
        
        // 转换为灰度 - 使用更安全的方法
        try {
            doc.changeMode(ChangeMode.GRAYSCALE);
        } catch (e) {
            // 如果直接转换失败，先尝试去饱和度
            try {
                var desc1 = new ActionDescriptor();
                executeAction(stringIDToTypeID("desaturate"), desc1, DialogModes.NO);
            } catch (desatError) {
                // 最后的备选方案：使用通道混合器
                applyChannelMixer(doc);
            }
        }
        
        // 反转（白热模式）
        try {
            var desc2 = new ActionDescriptor();
            executeAction(charIDToTypeID("Invr"), desc2, DialogModes.NO);
        } catch (e) {
            // 备选反转方法
            invertUsingCurves(doc);
        }
        
        // 调整对比度
        try {
            var desc3 = new ActionDescriptor();
            desc3.putInteger(charIDToTypeID("Brgh"), -5);
            desc3.putInteger(charIDToTypeID("Cntr"), 35);
            desc3.putBoolean(charIDToTypeID("useLegacy"), true);
            executeAction(charIDToTypeID("BrgC"), desc3, DialogModes.NO);
        } catch (e) {
            // 忽略对比度调整错误
        }
        
        // 轻微模糊
        try {
            var desc4 = new ActionDescriptor();
            desc4.putUnitDouble(charIDToTypeID("Rds "), charIDToTypeID("#Pxl"), 1.0);
            executeAction(charIDToTypeID("GsnB"), desc4, DialogModes.NO);
        } catch (e) {
            // 忽略模糊错误
        }
        
        // 应用像素化效果（如果需要）
        if (batchSettings.applyPixelation) {
            applyBatchPixelation(doc);
        }
        
    } catch (e) {
        throw new Error("热成像转换失败：" + e.message);
    }
}

function applyChannelMixer(doc) {
    try {
        var desc = new ActionDescriptor();
        var list = new ActionList();
        var channelDesc = new ActionDescriptor();
        
        channelDesc.putInteger(charIDToTypeID("Rd  "), 30);
        channelDesc.putInteger(charIDToTypeID("Grn "), 59);
        channelDesc.putInteger(charIDToTypeID("Bl  "), 11);
        channelDesc.putInteger(charIDToTypeID("Cnst"), 0);
        list.putObject(charIDToTypeID("ChMx"), channelDesc);
        
        desc.putList(charIDToTypeID("Adjs"), list);
        desc.putBoolean(charIDToTypeID("Mnch"), true);
        executeAction(charIDToTypeID("ChMx"), desc, DialogModes.NO);
    } catch (e) {
        // 如果通道混合器也失败，至少尝试去饱和度
        try {
            executeAction(stringIDToTypeID("desaturate"), new ActionDescriptor(), DialogModes.NO);
        } catch (desatError) {
            // 最后的备选方案已经尝试过了
        }
    }
}

function invertUsingCurves(doc) {
    try {
        var desc = new ActionDescriptor();
        var curveDesc = new ActionDescriptor();
        var pointList = new ActionList();
        
        // 添加曲线点以实现反转
        var point1 = new ActionDescriptor();
        point1.putDouble(charIDToTypeID("Hrzn"), 0);
        point1.putDouble(charIDToTypeID("Vrtc"), 255);
        pointList.putObject(charIDToTypeID("Pnt "), point1);
        
        var point2 = new ActionDescriptor();
        point2.putDouble(charIDToTypeID("Hrzn"), 255);
        point2.putDouble(charIDToTypeID("Vrtc"), 0);
        pointList.putObject(charIDToTypeID("Pnt "), point2);
        
        curveDesc.putList(charIDToTypeID("Crv "), pointList);
        desc.putObject(charIDToTypeID("Adjs"), curveDesc);
        executeAction(charIDToTypeID("Crvs"), desc, DialogModes.NO);
    } catch (e) {
        // 曲线反转失败，使用简单的反转
        try {
            executeAction(charIDToTypeID("Invr"), new ActionDescriptor(), DialogModes.NO);
        } catch (invertError) {
            // 反转失败，继续其他步骤
        }
    }
}

function applyBatchPixelation(doc) {
    try {
        var originalWidth = parseInt(doc.width.value);
        var originalHeight = parseInt(doc.height.value);
        
        var targetWidth, targetHeight;
        
        if (batchSettings.deviceType === "professional") {
            // 大疆H30T
            targetWidth = 1280;
            targetHeight = 1024;
        } else {
            // 传统热成像仪
            targetWidth = 320;
            targetHeight = 240;
        }
        
        // 如果原图已经很小，跳过像素化
        if (originalWidth <= targetWidth && originalHeight <= targetHeight) {
            return;
        }
        
        // 计算缩放比例
        var scaleX = targetWidth / originalWidth;
        var scaleY = targetHeight / originalHeight;
        var scale = Math.min(scaleX, scaleY);
        
        var newWidth = Math.round(originalWidth * scale);
        var newHeight = Math.round(originalHeight * scale);
        
        // 确保尺寸有效
        if (newWidth < 1) newWidth = 1;
        if (newHeight < 1) newHeight = 1;
        
        // 缩小
        doc.resizeImage(UnitValue(newWidth, "px"), UnitValue(newHeight, "px"), 72, ResampleMethod.BICUBIC);
        
        // 放大回原尺寸（保持像素化）
        doc.resizeImage(UnitValue(originalWidth, "px"), UnitValue(originalHeight, "px"), 72, ResampleMethod.NEARESTNEIGHBOR);
        
    } catch (e) {
        // 像素化失败，继续其他处理
        $.writeln("像素化处理失败：" + e.message);
    }
}

function saveProcessedFile(doc, outputFile) {
    try {
        var saveOptions;
        
        switch (batchSettings.outputFormat) {
            case "jpg":
                saveOptions = new JPEGSaveOptions();
                saveOptions.quality = batchSettings.outputQuality;
                saveOptions.embedColorProfile = true;
                saveOptions.formatOptions = FormatOptions.STANDARDBASELINE;
                doc.saveAs(outputFile, saveOptions);
                break;
                
            case "png":
                saveOptions = new PNGSaveOptions();
                saveOptions.compression = 6;
                saveOptions.interlaced = false;
                doc.saveAs(outputFile, saveOptions);
                break;
                
            case "tif":
                saveOptions = new TiffSaveOptions();
                saveOptions.compression = TIFFEncoding.NONE;
                saveOptions.imageCompression = TIFFEncoding.NONE;
                doc.saveAs(outputFile, saveOptions);
                break;
        }
    } catch (e) {
        throw new Error("保存文件失败：" + e.message);
    }
}

function saveProcessingLog(logMessages) {
    try {
        var logFile = new File(batchSettings.outputFolder + "/thermal_conversion_log.txt");
        logFile.open("w");
        logFile.write(logMessages.join("\n"));
        logFile.close();
    } catch (e) {
        // 忽略日志保存错误
        $.writeln("保存日志失败：" + e.message);
    }
}

// 单文件处理模式（保持向后兼容）
function singleThermalConversion() {
    if (app.documents.length == 0) {
        alert("请先打开一个图像文件！");
        return;
    }
    
    try {
        var doc = app.activeDocument;
        
        // 设置单文件处理的默认参数
        batchSettings.applyPixelation = confirm("是否应用像素化效果？");
        if (batchSettings.applyPixelation) {
            var deviceChoice = confirm("大疆H30T专业级？\n\n确定=H30T，取消=传统热成像仪");
            batchSettings.deviceType = deviceChoice ? "professional" : "traditional";
        }
        
        performBatchThermalConversion(doc);
        
        alert("单文件转换完成！");
        
    } catch (e) {
        alert("转换失败：" + e.message);
    }
}

// 主菜单
function showMainMenu() {
    try {
        var choice = confirm("选择处理模式：\n\n点击'确定' = 批量处理文件夹\n点击'取消' = 处理当前打开的图片");
        
        if (choice) {
            batchThermalConversion();
        } else {
            singleThermalConversion();
        }
    } catch (e) {
        alert("程序启动失败：" + e.message);
    }
}

// 启动主程序
showMainMenu();