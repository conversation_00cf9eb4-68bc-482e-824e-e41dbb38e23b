#!/usr/bin/env python3
"""
快速评估极简模型脚本
验证模型加载和基本功能，不依赖OpenCV
"""

import os
import sys
import argparse
import torch
import json
from datetime import datetime

# 添加路径
sys.path.append('.')
sys.path.append('src')

def main():
    parser = argparse.ArgumentParser(description='快速评估极简模型')
    parser.add_argument('--model_path', type=str, required=True, help='模型路径')
    parser.add_argument('--data_dir', type=str, default='data', help='数据目录')
    
    args = parser.parse_args()
    
    print("🚀 快速评估极简模型")
    print("=" * 50)
    print(f"📁 模型路径: {args.model_path}")
    print(f"📁 数据目录: {args.data_dir}")
    print("=" * 50)
    
    # 检查模型文件
    if not os.path.exists(args.model_path):
        print(f"❌ [ERROR] 模型文件不存在: {args.model_path}")
        return
    
    # 设置设备
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🖥️ [DEVICE] 使用设备: {device}")
    
    try:
        # 加载极简模型
        print(f"📦 [LOAD] 加载极简模型...")
        
        from src.models.minimal_multimodal import MinimalMultimodalYOLO
        
        # 创建模型
        model = MinimalMultimodalYOLO(nc=5)
        
        # 加载权重
        checkpoint = torch.load(args.model_path, map_location='cpu')
        if 'model_state_dict' in checkpoint:
            state_dict = checkpoint['model_state_dict']
        else:
            state_dict = checkpoint
        
        model.load_state_dict(state_dict)
        model = model.to(device)
        model.eval()
        
        print("✅ [SUCCESS] 模型加载成功")
        
        # 测试前向传播
        print(f"🧪 [TEST] 测试前向传播...")
        
        with torch.no_grad():
            # 创建测试输入
            rgb_input = torch.randn(1, 3, 640, 640).to(device)
            thermal_input = torch.randn(1, 1, 640, 640).to(device)
            
            # 前向传播
            outputs = model(rgb_input, thermal_input)
            
            print(f"✅ [SUCCESS] 前向传播成功")
            print(f"📊 [OUTPUT] 输出形状: {[out.shape for out in outputs]}")
        
        # 检查数据集
        print(f"📊 [DATA] 检查数据集...")
        
        test_images_dir = os.path.join(args.data_dir, 'test', 'images')
        test_labels_dir = os.path.join(args.data_dir, 'test', 'labels')
        
        if os.path.exists(test_images_dir):
            import glob
            image_files = glob.glob(os.path.join(test_images_dir, '*.jpg')) + \
                          glob.glob(os.path.join(test_images_dir, '*.png'))
            print(f"📊 [DATA] 测试图像: {len(image_files)} 张")
        else:
            print(f"⚠️ [WARN] 测试图像目录不存在: {test_images_dir}")
        
        if os.path.exists(test_labels_dir):
            import glob
            label_files = glob.glob(os.path.join(test_labels_dir, '*.txt'))
            print(f"📊 [DATA] 标签文件: {len(label_files)} 个")
        else:
            print(f"⚠️ [WARN] 测试标签目录不存在: {test_labels_dir}")
        
        # 分析训练信息
        if 'epoch' in checkpoint:
            print(f"📊 [TRAIN] 训练轮数: {checkpoint['epoch']}")
        
        if 'loss' in checkpoint:
            print(f"📊 [TRAIN] 最终损失: {checkpoint['loss']:.4f}")
        
        # 创建评估报告
        report = {
            'model_type': 'minimal',
            'model_path': args.model_path,
            'model_loaded': True,
            'forward_pass_success': True,
            'device': device,
            'timestamp': datetime.now().isoformat(),
            'model_info': {
                'total_params': sum(p.numel() for p in model.parameters()),
                'trainable_params': sum(p.numel() for p in model.parameters() if p.requires_grad),
                'model_size_mb': sum(p.numel() for p in model.parameters()) * 4 / 1024 / 1024
            }
        }
        
        # 保存报告
        output_dir = 'runs/quick_eval'
        os.makedirs(output_dir, exist_ok=True)
        
        report_file = os.path.join(output_dir, 'quick_eval_report.json')
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📊 [SUMMARY] 评估总结:")
        print(f"  ✅ 模型加载: 成功")
        print(f"  ✅ 前向传播: 成功")
        print(f"  📊 模型参数: {report['model_info']['total_params']:,}")
        print(f"  📊 模型大小: {report['model_info']['model_size_mb']:.2f} MB")
        print(f"  🖥️ 运行设备: {device}")
        
        print(f"\n💾 [SAVE] 报告已保存到: {report_file}")
        
        print(f"\n💡 [NEXT] 下一步建议:")
        print(f"  1. 模型基本功能正常，可以进行完整评估")
        print(f"  2. 安装OpenCV进行图像处理: pip install opencv-python")
        print(f"  3. 使用完整评估脚本: python evaluate_minimal_complete.py")
        print(f"  4. 对比不同置信度阈值的效果")
        
        # 检查是否是课程学习训练的模型
        if 'curriculum' in args.model_path:
            print(f"\n🎓 [CURRICULUM] 检测到课程学习训练模型:")
            print(f"  📚 这是使用课程学习策略训练的极简模型")
            print(f"  🎯 应该对Glass_Loss和insulator类别有更好的检测性能")
            print(f"  📊 建议使用低置信度阈值(0.01-0.05)进行评估")
        
    except Exception as e:
        print(f"❌ [ERROR] 评估失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
