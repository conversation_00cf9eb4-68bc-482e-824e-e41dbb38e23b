#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复已训练模型的置信度偏置脚本
解决评估指标为0的问题
"""

import os
import sys
import torch
import argparse

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from src.models.multimodal_yolo_fixed import FixedMultimodalYOLO


def fix_model_confidence_bias(model_path, output_path=None):
    """修复模型的置信度偏置"""
    print("🔧 [FIX] 开始修复模型置信度偏置...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 1. 加载原始模型
    print(f"📁 [LOAD] 加载模型: {model_path}")
    checkpoint = torch.load(model_path, map_location=device)
    
    # 2. 创建模型实例
    model = FixedMultimodalYOLO(nc=5, fusion_type='cross_attention').to(device)
    
    # 3. 加载权重
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
        print("✅ [LOAD] 从checkpoint加载模型权重")
    else:
        model.load_state_dict(checkpoint)
        print("✅ [LOAD] 直接加载模型权重")
    
    # 4. 检查当前置信度偏置
    print("\n🔍 [CHECK] 检查当前置信度偏置...")
    for i, head in enumerate(model.detection_head.heads):
        output_layer = head[-1]
        if hasattr(output_layer, 'bias') and output_layer.bias is not None:
            outputs_per_anchor = 5 + 5  # nc + 5
            conf_biases = []
            for anchor_idx in range(3):
                start_idx = anchor_idx * outputs_per_anchor
                conf_bias = output_layer.bias[start_idx + 4].item()
                conf_biases.append(conf_bias)
            print(f"  检测头{i}: 置信度偏置 = {conf_biases}")
    
    # 5. 修复置信度偏置
    print("\n🔧 [FIX] 修复置信度偏置...")

    # 直接修复检测头的置信度偏置
    for head in model.detection_head.heads:
        output_layer = head[-1]

        if hasattr(output_layer, 'bias') and output_layer.bias is not None:
            with torch.no_grad():
                outputs_per_anchor = 5 + 5  # nc + 5

                for anchor_idx in range(3):  # 3个anchor
                    start_idx = anchor_idx * outputs_per_anchor
                    # 强制设置置信度偏置为-4.6
                    output_layer.bias[start_idx + 4] = -4.6

    print("✅ [FIX] 置信度偏置已修复为-4.6")
    
    # 6. 验证修复结果
    print("\n✅ [VERIFY] 验证修复结果...")
    for i, head in enumerate(model.detection_head.heads):
        output_layer = head[-1]
        if hasattr(output_layer, 'bias') and output_layer.bias is not None:
            outputs_per_anchor = 5 + 5  # nc + 5
            conf_biases = []
            for anchor_idx in range(3):
                start_idx = anchor_idx * outputs_per_anchor
                conf_bias = output_layer.bias[start_idx + 4].item()
                conf_biases.append(conf_bias)
            print(f"  检测头{i}: 置信度偏置 = {conf_biases}")
    
    # 7. 测试模型输出
    print("\n🧪 [TEST] 测试修复后的模型输出...")
    model.eval()
    with torch.no_grad():
        # 创建虚拟输入
        rgb_input = torch.randn(1, 3, 512, 512).to(device)
        thermal_input = torch.randn(1, 3, 512, 512).to(device)
        
        try:
            outputs = model(rgb_input, thermal_input)
            print(f"📤 [TEST] 模型输出数量: {len(outputs)}")
            
            for j, output in enumerate(outputs):
                if output.numel() > 0:
                    pred = output[0]  # 第一个batch
                    if pred.shape[0] > 0:
                        obj_conf_raw = pred[:, 4]  # 原始对象置信度
                        obj_conf = torch.sigmoid(obj_conf_raw)
                        
                        print(f"  输出{j}: 原始置信度范围 [{obj_conf_raw.min():.4f}, {obj_conf_raw.max():.4f}]")
                        print(f"  输出{j}: Sigmoid置信度范围 [{obj_conf.min():.4f}, {obj_conf.max():.4f}]")
                        print(f"  输出{j}: 最高置信度 {obj_conf.max():.4f}")
                        
                        # 统计不同阈值下的检测数量
                        thresholds = [0.001, 0.01, 0.05, 0.1, 0.25]
                        for thresh in thresholds:
                            count = (obj_conf > thresh).sum().item()
                            print(f"  输出{j}: 置信度>{thresh}: {count}个检测")
        
        except Exception as e:
            print(f"❌ [ERROR] 模型测试失败: {e}")
            return False
    
    # 8. 保存修复后的模型
    if output_path is None:
        output_path = model_path.replace('.pt', '_fixed_confidence.pt')
    
    print(f"\n💾 [SAVE] 保存修复后的模型: {output_path}")
    
    # 更新checkpoint
    if 'model_state_dict' in checkpoint:
        checkpoint['model_state_dict'] = model.state_dict()
        # 添加修复标记
        checkpoint['confidence_bias_fixed'] = True
        import time
        checkpoint['fix_timestamp'] = time.time()
    else:
        import time
        checkpoint = {
            'model_state_dict': model.state_dict(),
            'confidence_bias_fixed': True,
            'fix_timestamp': time.time()
        }
    
    torch.save(checkpoint, output_path)
    
    print("🎉 [SUCCESS] 模型置信度偏置修复完成！")
    print(f"\n📋 [USAGE] 使用修复后的模型进行评估:")
    print(f"python evaluate.py --model_path {output_path} --conf_thresh 0.01")
    
    return True


def main():
    parser = argparse.ArgumentParser(description='修复模型置信度偏置')
    parser.add_argument('--model_path', type=str, required=True, help='原始模型路径')
    parser.add_argument('--output_path', type=str, help='输出模型路径（可选）')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.model_path):
        print(f"❌ [ERROR] 模型文件不存在: {args.model_path}")
        return
    
    success = fix_model_confidence_bias(args.model_path, args.output_path)
    
    if success:
        print("\n✅ 修复成功！现在可以重新评估模型了。")
    else:
        print("\n❌ 修复失败！请检查错误信息。")


if __name__ == "__main__":
    main()
