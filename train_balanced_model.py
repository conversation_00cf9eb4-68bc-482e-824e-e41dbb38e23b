#!/usr/bin/env python3
"""
针对类别不平衡问题的专门训练脚本
使用激进的类别权重和Focal Loss
"""

import os
import sys
import yaml
import torch
import torch.nn as nn
from datetime import datetime

# 添加路径
sys.path.append('.')
sys.path.append('src')

from src.training.train_multimodal import MultimodalTrainer

class BalancedTrainer(MultimodalTrainer):
    """专门处理类别不平衡的训练器"""
    
    def __init__(self, config):
        super().__init__(config)
        
        # 设置类别权重
        self.class_weights = config.get('class_weights', [1.0, 2.0, 8.0, 3.0, 10.0])
        print(f"🎯 [BALANCED] 使用类别权重: {self.class_weights}")
        
        # Focal Loss配置
        self.use_focal_loss = config.get('loss_config', {}).get('use_focal_loss', True)
        self.focal_alpha = config.get('loss_config', {}).get('focal_alpha', 0.25)
        self.focal_gamma = config.get('loss_config', {}).get('focal_gamma', 2.0)
        
        if self.use_focal_loss:
            print(f"🔥 [FOCAL] 使用Focal Loss: alpha={self.focal_alpha}, gamma={self.focal_gamma}")
    
    def create_loss_function(self):
        """创建带类别权重的损失函数"""
        if self.use_focal_loss:
            return self.create_focal_loss()
        else:
            return self.create_weighted_loss()
    
    def create_focal_loss(self):
        """创建Focal Loss"""
        class FocalLoss(nn.Module):
            def __init__(self, alpha=0.25, gamma=2.0, class_weights=None):
                super().__init__()
                self.alpha = alpha
                self.gamma = gamma
                self.class_weights = torch.tensor(class_weights) if class_weights else None
                
            def forward(self, pred, target):
                ce_loss = nn.CrossEntropyLoss(weight=self.class_weights)(pred, target)
                pt = torch.exp(-ce_loss)
                focal_loss = self.alpha * (1-pt)**self.gamma * ce_loss
                return focal_loss
        
        return FocalLoss(
            alpha=self.focal_alpha,
            gamma=self.focal_gamma,
            class_weights=self.class_weights
        )
    
    def create_weighted_loss(self):
        """创建加权损失函数"""
        class_weights_tensor = torch.tensor(self.class_weights, dtype=torch.float32)
        return nn.CrossEntropyLoss(weight=class_weights_tensor)
    
    def train_epoch(self, epoch):
        """训练一个epoch，增加类别平衡监控"""
        print(f"\n📚 [EPOCH {epoch}] 开始类别平衡训练...")
        
        # 调用父类的训练方法
        epoch_loss = super().train_epoch(epoch)
        
        # 每10个epoch打印类别权重提醒
        if epoch % 10 == 0:
            print(f"🎯 [WEIGHTS] 当前类别权重: {self.class_weights}")
            print("   - Glass_Loss (类别2): 权重 x8")
            print("   - insulator (类别4): 权重 x10")
            print("   - Polyme_Dirty (类别3): 权重 x3")
        
        return epoch_loss
    
    def evaluate_with_class_metrics(self, dataloader):
        """评估时计算每个类别的详细指标"""
        print("📊 [EVAL] 计算类别详细指标...")
        
        # 这里可以添加更详细的类别级别评估
        # 暂时使用父类的评估方法
        return super().evaluate(dataloader)

def create_balanced_config():
    """创建类别平衡训练配置"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    config = {
        'data_dir': 'data',
        'output_dir': f'runs/train_balanced/{timestamp}',
        'nc': 5,
        'img_size': 512,
        'batch_size': 4,
        'epochs': 150,
        'num_workers': 0,
        'grad_clip': 5.0,
        
        # 激进的类别权重
        'class_weights': [1.0, 2.0, 8.0, 3.0, 10.0],
        
        'model': {
            'type': 'simple',
            'yolo_model_path': 'yolov8n.pt',
            'fusion_type': 'cross_attention'
        },
        
        'optimizer': {
            'type': 'Adam',
            'lr': 0.0001,
            'weight_decay': 0.0005
        },
        
        'scheduler': {
            'type': 'CosineAnnealingLR',
            'T_max': 150,
            'eta_min': 1e-7
        },
        
        'early_stopping': {
            'patience': 30
        },
        
        # Focal Loss配置
        'loss_config': {
            'use_focal_loss': True,
            'focal_alpha': 0.25,
            'focal_gamma': 2.0
        },
        
        # 损失权重
        'loss_weights': {
            'box_loss': 1.0,
            'obj_loss': 3.0,
            'cls_loss': 2.0
        },
        
        # 评估配置
        'evaluation': {
            'conf_thresh': 0.05,  # 低置信度阈值
            'iou_thresh': 0.5
        }
    }
    
    return config

def main():
    print("🎯 类别平衡训练")
    print("=" * 50)
    print("目标：解决Glass_Loss和insulator类别AP为0的问题")
    print("策略：激进类别权重 + Focal Loss + 低置信度阈值")
    print("=" * 50)
    
    # 检查是否有配置文件
    config_file = 'configs/config_class_balanced.yaml'
    if os.path.exists(config_file):
        print(f"📄 [CONFIG] 加载配置文件: {config_file}")
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
    else:
        print("📄 [CONFIG] 使用默认平衡配置")
        config = create_balanced_config()
    
    print("\n🎯 [STRATEGY] 类别平衡策略:")
    class_weights = config.get('class_weights', [1.0, 2.0, 8.0, 3.0, 10.0])
    class_names = ['110_two_hight_glass', 'Glass_Dirty', 'Glass_Loss', 'Polyme_Dirty', 'insulator']
    
    for i, (name, weight) in enumerate(zip(class_names, class_weights)):
        print(f"  类别{i} ({name}): 权重 x{weight}")
    
    print(f"\n🔥 [FOCAL] Focal Loss参数:")
    loss_config = config.get('loss_config', {})
    print(f"  alpha: {loss_config.get('focal_alpha', 0.25)}")
    print(f"  gamma: {loss_config.get('focal_gamma', 2.0)}")
    
    print(f"\n📉 [EVAL] 评估配置:")
    eval_config = config.get('evaluation', {})
    print(f"  置信度阈值: {eval_config.get('conf_thresh', 0.05)}")
    print(f"  IoU阈值: {eval_config.get('iou_thresh', 0.5)}")
    
    # 开始训练
    try:
        trainer = BalancedTrainer(config)
        print(f"\n🚀 [START] 开始类别平衡训练...")
        trainer.train()
        print(f"\n🎉 [SUCCESS] 训练完成!")
        
        # 输出训练结果位置
        output_dir = config['output_dir']
        print(f"\n📁 [OUTPUT] 训练结果保存在: {output_dir}")
        print(f"  最佳模型: {output_dir}/weights/best.pt")
        print(f"  最新模型: {output_dir}/weights/last.pt")
        
        print(f"\n🔍 [NEXT] 建议下一步:")
        print(f"  1. 使用低置信度阈值评估: python evaluate.py --conf_thresh 0.05")
        print(f"  2. 检查各类别的详细指标")
        print(f"  3. 如果效果仍不理想，考虑进一步增加少数类别权重")
        
    except Exception as e:
        print(f"❌ [ERROR] 训练失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
