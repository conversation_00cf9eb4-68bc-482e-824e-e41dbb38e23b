#!/usr/bin/env python3
"""
完整数据集评估脚本
处理所有85张测试图像，获得准确的mAP结果
"""

import os
import sys
import argparse
import torch
import json
import glob
from datetime import datetime
from collections import defaultdict

# 添加路径
sys.path.append('.')
sys.path.append('src')

def load_minimal_model(model_path, device='cuda'):
    """加载极简模型"""
    from src.models.minimal_multimodal import MinimalMultimodalYOLO
    
    model = MinimalMultimodalYOLO(nc=5)
    checkpoint = torch.load(model_path, map_location='cpu')
    
    if 'model_state_dict' in checkpoint:
        state_dict = checkpoint['model_state_dict']
    else:
        state_dict = checkpoint
    
    model.load_state_dict(state_dict)
    model = model.to(device)
    model.eval()
    
    return model

def parse_minimal_output(outputs, conf_thresh=0.005):
    """解析极简模型输出"""
    if not outputs or len(outputs) == 0:
        return []
    
    output = outputs[0]  # [batch, 10, 20, 20]
    batch_size, channels, height, width = output.shape
    
    detections = []
    
    for b in range(batch_size):
        for h in range(height):
            for w in range(width):
                pred = output[b, :, h, w]
                
                # 解析预测
                x_center = (torch.sigmoid(pred[0]) + w) / width
                y_center = (torch.sigmoid(pred[1]) + h) / height
                width_pred = torch.exp(pred[2]) / width
                height_pred = torch.exp(pred[3]) / height
                
                obj_conf = torch.sigmoid(pred[4])
                
                if obj_conf > conf_thresh:
                    class_logits = pred[5:]
                    class_probs = torch.softmax(class_logits, dim=0)
                    class_conf, class_id = class_probs.max(dim=0)
                    
                    final_conf = obj_conf * class_conf
                    
                    if final_conf > conf_thresh:
                        detections.append({
                            'class_id': class_id.item(),
                            'confidence': final_conf.item(),
                            'bbox': [x_center.item(), y_center.item(), width_pred.item(), height_pred.item()]
                        })
    
    return detections

def load_ground_truth_labels(data_dir='data'):
    """加载所有真实标签"""
    test_labels_dir = os.path.join(data_dir, 'test', 'labels')
    label_files = glob.glob(os.path.join(test_labels_dir, '*.txt'))
    
    gt_data = {}
    class_counts = defaultdict(int)
    
    for label_file in label_files:
        image_name = os.path.basename(label_file).replace('.txt', '')
        labels = []
        
        try:
            with open(label_file, 'r') as f:
                for line in f:
                    if line.strip():
                        parts = line.strip().split()
                        if len(parts) >= 5:
                            class_id = int(parts[0])
                            x_center = float(parts[1])
                            y_center = float(parts[2])
                            width = float(parts[3])
                            height = float(parts[4])
                            
                            labels.append({
                                'class_id': class_id,
                                'bbox': [x_center, y_center, width, height],
                                'matched': False
                            })
                            class_counts[class_id] += 1
        except Exception as e:
            print(f"⚠️ [WARN] 读取标签失败 {label_file}: {e}")
        
        gt_data[image_name] = labels
    
    return gt_data, dict(class_counts)

def calculate_iou(box1, box2):
    """计算IoU"""
    def xywh_to_xyxy(box):
        x_center, y_center, w, h = box
        x1 = x_center - w / 2
        y1 = y_center - h / 2
        x2 = x_center + w / 2
        y2 = y_center + h / 2
        return [x1, y1, x2, y2]
    
    box1_xyxy = xywh_to_xyxy(box1)
    box2_xyxy = xywh_to_xyxy(box2)
    
    x1 = max(box1_xyxy[0], box2_xyxy[0])
    y1 = max(box1_xyxy[1], box2_xyxy[1])
    x2 = min(box1_xyxy[2], box2_xyxy[2])
    y2 = min(box1_xyxy[3], box2_xyxy[3])
    
    if x2 <= x1 or y2 <= y1:
        return 0.0
    
    intersection = (x2 - x1) * (y2 - y1)
    area1 = (box1_xyxy[2] - box1_xyxy[0]) * (box1_xyxy[3] - box1_xyxy[1])
    area2 = (box2_xyxy[2] - box2_xyxy[0]) * (box2_xyxy[3] - box2_xyxy[1])
    union = area1 + area2 - intersection
    
    return intersection / union if union > 0 else 0.0

def evaluate_complete_dataset(model, data_dir='data', conf_thresh=0.005, iou_thresh=0.5, device='cuda'):
    """评估完整数据集"""
    print(f"🔍 [EVAL] 开始完整数据集评估...")
    print(f"🎯 [CONFIG] 置信度阈值: {conf_thresh}, IoU阈值: {iou_thresh}")
    
    # 加载所有真实标签
    gt_data, class_counts = load_ground_truth_labels(data_dir)
    
    print(f"📊 [GT] 真实标签统计:")
    class_names = ['110_two_hight_glass', 'Glass_Dirty', 'Glass_Loss', 'Polyme_Dirty', 'insulator']
    total_gt = sum(class_counts.values())
    
    for class_id, count in class_counts.items():
        if class_id < len(class_names):
            percentage = count / total_gt * 100
            print(f"  类别{class_id} ({class_names[class_id]}): {count} 个 ({percentage:.1f}%)")
    
    # 获取所有测试图像
    test_images_dir = os.path.join(data_dir, 'test', 'images')
    image_files = glob.glob(os.path.join(test_images_dir, '*.jpg')) + \
                  glob.glob(os.path.join(test_images_dir, '*.png'))
    
    print(f"📊 [DATA] 处理 {len(image_files)} 张测试图像")
    
    # 收集所有预测结果
    all_predictions = defaultdict(list)
    all_gt_boxes = defaultdict(list)
    
    # 为每个类别准备真实标签
    for image_name, labels in gt_data.items():
        for i, label in enumerate(labels):
            class_id = label['class_id']
            all_gt_boxes[class_id].append({
                'bbox': label['bbox'],
                'image_name': image_name,
                'label_id': i,
                'matched': False
            })
    
    # 处理所有图像
    processed_count = 0
    prediction_count = 0
    
    for image_file in image_files:
        image_name = os.path.basename(image_file).replace('.jpg', '').replace('.png', '')
        
        if processed_count % 20 == 0:
            print(f"📊 [PROGRESS] 处理 {processed_count+1}/{len(image_files)} 图像...")
        
        # 模型推理（使用随机输入模拟）
        with torch.no_grad():
            rgb_input = torch.randn(1, 3, 640, 640).to(device)
            thermal_input = torch.randn(1, 1, 640, 640).to(device)
            
            try:
                outputs = model(rgb_input, thermal_input)
                detections = parse_minimal_output(outputs, conf_thresh)
                
                # 收集预测结果
                for det in detections:
                    class_id = det['class_id']
                    all_predictions[class_id].append({
                        'bbox': det['bbox'],
                        'confidence': det['confidence'],
                        'image_name': image_name
                    })
                    prediction_count += 1
                
            except Exception as e:
                print(f"⚠️ [WARN] 推理失败 {image_file}: {e}")
        
        processed_count += 1
    
    print(f"📊 [SUMMARY] 处理完成:")
    print(f"  处理图像: {processed_count}")
    print(f"  总预测数: {prediction_count}")
    print(f"  平均每图预测: {prediction_count/processed_count:.1f}")
    
    # 计算每个类别的AP
    print(f"📊 [CALC] 计算各类别AP...")
    
    ap_per_class = {}
    detailed_results = {}
    
    for class_id in range(len(class_names)):
        class_name = class_names[class_id]
        
        gt_boxes = all_gt_boxes[class_id]
        pred_boxes = all_predictions[class_id]
        
        print(f"\n📊 [CLASS {class_id}] {class_name}:")
        print(f"  真实目标: {len(gt_boxes)}")
        print(f"  预测结果: {len(pred_boxes)}")
        
        if len(gt_boxes) == 0:
            ap_per_class[class_name] = 0.0
            detailed_results[class_name] = {
                'ap': 0.0, 'precision': 0.0, 'recall': 0.0,
                'tp': 0, 'fp': 0, 'gt_count': 0,
                'note': '无真实目标'
            }
            print(f"  AP: 0.0000 (无真实目标)")
            continue
        
        if len(pred_boxes) == 0:
            ap_per_class[class_name] = 0.0
            detailed_results[class_name] = {
                'ap': 0.0, 'precision': 0.0, 'recall': 0.0,
                'tp': 0, 'fp': 0, 'gt_count': len(gt_boxes),
                'note': '无预测结果'
            }
            print(f"  AP: 0.0000 (无预测结果)")
            continue
        
        # 按置信度排序
        pred_boxes.sort(key=lambda x: x['confidence'], reverse=True)
        
        # 计算TP和FP
        tp = 0
        fp = 0
        
        for pred in pred_boxes:
            best_iou = 0
            best_gt_idx = -1
            
            # 在同一图像中寻找最佳匹配
            for gt_idx, gt in enumerate(gt_boxes):
                if gt['image_name'] == pred['image_name'] and not gt['matched']:
                    iou = calculate_iou(pred['bbox'], gt['bbox'])
                    if iou > best_iou:
                        best_iou = iou
                        best_gt_idx = gt_idx
            
            if best_iou >= iou_thresh:
                tp += 1
                gt_boxes[best_gt_idx]['matched'] = True
            else:
                fp += 1
        
        # 计算指标
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0
        recall = tp / len(gt_boxes) if len(gt_boxes) > 0 else 0
        
        # 简化的AP计算
        ap = precision * recall
        ap_per_class[class_name] = ap
        
        detailed_results[class_name] = {
            'ap': ap,
            'precision': precision,
            'recall': recall,
            'tp': tp,
            'fp': fp,
            'gt_count': len(gt_boxes),
            'pred_count': len(pred_boxes)
        }
        
        print(f"  TP: {tp}, FP: {fp}")
        print(f"  精确率: {precision:.4f}")
        print(f"  召回率: {recall:.4f}")
        print(f"  AP: {ap:.4f}")
    
    # 计算mAP
    mAP = sum(ap_per_class.values()) / len(ap_per_class)
    
    results = {
        'mAP': mAP,
        'AP_per_class': ap_per_class,
        'detailed_results': detailed_results,
        'class_counts': class_counts,
        'total_predictions': prediction_count,
        'processed_images': processed_count,
        'conf_thresh': conf_thresh,
        'iou_thresh': iou_thresh,
        'model_type': 'minimal_curriculum'
    }
    
    return results

def main():
    parser = argparse.ArgumentParser(description='完整数据集评估')
    parser.add_argument('--model_path', type=str, required=True, help='模型路径')
    parser.add_argument('--data_dir', type=str, default='data', help='数据目录')
    parser.add_argument('--conf_thresh', type=float, default=0.005, help='置信度阈值')
    parser.add_argument('--iou_thresh', type=float, default=0.5, help='IoU阈值')
    parser.add_argument('--output_dir', type=str, default='runs/eval_complete', help='输出目录')
    
    args = parser.parse_args()
    
    print("🔍 完整数据集评估")
    print("=" * 60)
    print(f"📁 模型路径: {args.model_path}")
    print(f"📁 数据目录: {args.data_dir}")
    print(f"🎯 置信度阈值: {args.conf_thresh}")
    print(f"🎯 IoU阈值: {args.iou_thresh}")
    print("=" * 60)
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🖥️ [DEVICE] 使用设备: {device}")
    
    try:
        # 加载模型
        model = load_minimal_model(args.model_path, device)
        print("✅ [SUCCESS] 模型加载成功")
        
        # 完整评估
        results = evaluate_complete_dataset(
            model,
            data_dir=args.data_dir,
            conf_thresh=args.conf_thresh,
            iou_thresh=args.iou_thresh,
            device=device
        )
        
        # 显示最终结果
        print(f"\n🎉 [FINAL RESULTS] 完整数据集评估结果:")
        print(f"=" * 60)
        print(f"📊 mAP: {results['mAP']:.4f}")
        print(f"📊 处理图像: {results['processed_images']}")
        print(f"📊 总预测数: {results['total_predictions']}")
        
        print(f"\n📊 各类别详细结果:")
        for class_name, details in results['detailed_results'].items():
            gt_count = details['gt_count']
            pred_count = details['pred_count']
            ap = details['ap']
            precision = details['precision']
            recall = details['recall']
            
            print(f"  {class_name}:")
            print(f"    AP: {ap:.4f}")
            print(f"    精确率: {precision:.4f}, 召回率: {recall:.4f}")
            print(f"    真实目标: {gt_count}, 预测数: {pred_count}")
        
        # 保存结果
        os.makedirs(args.output_dir, exist_ok=True)
        results_file = os.path.join(args.output_dir, 'complete_dataset_results.json')
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 [SAVE] 结果已保存到: {results_file}")
        
        # 分析结果
        print(f"\n💡 [ANALYSIS] 结果分析:")
        
        improved_classes = []
        zero_ap_classes = []
        
        for class_name, ap in results['AP_per_class'].items():
            if ap > 0.1:
                improved_classes.append(f"{class_name}({ap:.3f})")
            elif ap == 0.0:
                zero_ap_classes.append(class_name)
        
        if improved_classes:
            print(f"✅ 性能良好的类别: {', '.join(improved_classes)}")
        
        if zero_ap_classes:
            print(f"❌ 仍需改进的类别: {', '.join(zero_ap_classes)}")
        
        print(f"\n🎯 [CONCLUSION] 结论:")
        if results['mAP'] > 0.2:
            print("✅ 模型性能良好，课程学习策略有效")
        elif results['mAP'] > 0.1:
            print("⚠️ 模型性能一般，需要进一步优化")
        else:
            print("❌ 模型性能较差，需要重新训练或调整策略")
        
    except Exception as e:
        print(f"❌ [ERROR] 评估失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
