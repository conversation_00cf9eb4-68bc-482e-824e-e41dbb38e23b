#!/usr/bin/env python3
"""
调试评估过程，找出指标为0的具体原因
"""

import torch
import numpy as np
import sys
import os

# 添加src目录到Python路径
sys.path.append('src')
sys.path.append('.')

from src.models.multimodal_yolo_fixed import FixedMultimodalYOLO as SimpleMultimodalYOLO
from src.dataset.multimodal_dataset import create_dataloader
import yaml

def debug_evaluation():
    """调试评估过程"""
    print("🔍 开始诊断评估问题...")
    
    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 加载类别名称
    with open('configs/data.yaml', 'r', encoding='utf-8') as f:
        data_config = yaml.safe_load(f)
    class_names = data_config['names']
    nc = len(class_names)
    print(f"类别数: {nc}")
    print(f"类别名称: {class_names}")
    
    # 创建模型
    model = SimpleMultimodalYOLO(nc=nc, fusion_type='cross_attention')
    
    # 加载权重
    model_path = 'runs/train_optimized/20250730_131733/weights/best.pt'
    print(f"加载模型: {model_path}")
    
    checkpoint = torch.load(model_path, map_location=device)
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
    else:
        model.load_state_dict(checkpoint)
    
    model = model.to(device)
    model.eval()
    print("✅ 模型加载完成")
    
    # 创建测试数据加载器（只测试1个batch）
    test_loader = create_dataloader(
        data_dir='data',
        split='test',
        batch_size=2,  # 小batch便于调试
        img_size=640,
        num_workers=0,
        shuffle=False
    )
    
    print(f"数据集大小: {len(test_loader.dataset)} 张图像")
    
    # 测试一个batch
    with torch.no_grad():
        for batch_idx, batch in enumerate(test_loader):
            rgb_images = batch['rgb'].to(device)
            thermal_images = batch['thermal'].to(device)
            targets = batch['targets']
            filenames = batch['filenames']
            
            print(f"\n=== Batch {batch_idx} ===")
            print(f"RGB形状: {rgb_images.shape}")
            print(f"热红外形状: {thermal_images.shape}")
            print(f"目标形状: {targets.shape}")
            print(f"文件名: {filenames}")
            
            # 模型推理
            outputs = model(rgb_images, thermal_images)
            print(f"模型输出数量: {len(outputs)}")
            for i, output in enumerate(outputs):
                print(f"  输出{i}形状: {output.shape}")
                print(f"  输出{i}范围: [{output.min().item():.4f}, {output.max().item():.4f}]")
            
            # 检查原始输出的置信度
            for i, output in enumerate(outputs):
                for b in range(output.shape[0]):
                    pred = output[b]  # [num_anchors*H*W, num_classes + 5]
                    if pred.shape[0] > 0:
                        obj_conf = torch.sigmoid(pred[..., 4])  # 对象置信度
                        cls_conf = torch.sigmoid(pred[..., 5:])  # 类别置信度
                        max_cls_conf, _ = torch.max(cls_conf, dim=-1)
                        final_conf = obj_conf * max_cls_conf
                        
                        print(f"  尺度{i}批次{b}: 对象置信度范围 [{obj_conf.min().item():.4f}, {obj_conf.max().item():.4f}]")
                        print(f"  尺度{i}批次{b}: 最大类别置信度范围 [{max_cls_conf.min().item():.4f}, {max_cls_conf.max().item():.4f}]")
                        print(f"  尺度{i}批次{b}: 最终置信度范围 [{final_conf.min().item():.4f}, {final_conf.max().item():.4f}]")
                        
                        # 检查有多少预测超过阈值
                        conf_thresh = 0.25
                        valid_count = (final_conf > conf_thresh).sum().item()
                        print(f"  尺度{i}批次{b}: 超过阈值{conf_thresh}的预测数: {valid_count}/{final_conf.shape[0]}")
            
            # 检查真实标签
            print(f"\n真实标签分析:")
            for i in range(rgb_images.shape[0]):
                img_targets = targets[targets[:, 0] == i]
                print(f"  图像{i} ({filenames[i]}): {len(img_targets)} 个目标")
                for j, target in enumerate(img_targets):
                    print(f"    目标{j}: 类别={int(target[1])}, bbox=[{target[2]:.3f}, {target[3]:.3f}, {target[4]:.3f}, {target[5]:.3f}]")
            
            # 只测试第一个batch
            break
    
    print("\n🎯 诊断建议:")
    print("1. 检查模型输出的置信度是否过低")
    print("2. 检查置信度阈值是否设置过高")
    print("3. 检查真实标签的格式是否正确")

if __name__ == '__main__':
    debug_evaluation()