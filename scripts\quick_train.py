#!/usr/bin/env python3
"""
一键快速训练脚本 - 最简单的使用方式
专门为Windows用户设计，无需复杂配置
"""

import os

# 添加src目录到Python路径
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import sys
import platform
import argparse

def main():
    print("🚀 绝缘子串多模态检测 - 一键快速训练")
    print("=" * 60)
    
    # 检测系统
    is_windows = platform.system() == 'Windows'
    if is_windows:
        print("✓ Windows系统检测到，使用Windows专用脚本")
        script = "train_windows_fix.py"
    else:
        print("✓ 非Windows系统，使用通用训练脚本")
        script = "train_multimodal.py"
    
    # 检查脚本是否存在
    if not os.path.exists(script):
        print(f"❌ 训练脚本不存在: {script}")
        print("请确保所有文件都已正确放置")
        return
    
    # 设置参数
    parser = argparse.ArgumentParser(description='一键快速训练')
    parser.add_argument('--epochs', type=int, default=10, help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=2, help='批次大小')
    parser.add_argument('--test', action='store_true', help='快速测试模式')
    
    args = parser.parse_args()
    
    # 构建命令
    if args.test:
        # 测试模式：最小配置
        cmd = f"python {script} --epochs 1 --batch_size 1"
        print("🧪 快速测试模式")
    else:
        # 正常训练模式
        cmd = f"python {script} --epochs {args.epochs} --batch_size {args.batch_size}"
        print("📚 正常训练模式")
    
    print(f"执行命令: {cmd}")
    print("=" * 60)
    
    # 执行训练
    os.system(cmd)

if __name__ == '__main__':
    main() 