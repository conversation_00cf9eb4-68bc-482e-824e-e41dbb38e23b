#!/usr/bin/env python3
"""
多模态绝缘子检测系统演示脚本

这个脚本展示了如何使用我们的多模态检测系统进行训练、评估和推理的完整流程。
"""

import os
import sys
import argparse
import yaml
import torch
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append('.')
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from src.dataset.multimodal_dataset import create_dataloader
from src.training.train_multimodal import MultimodalTrainer, create_default_config
from src.training.evaluate_multimodal import MultimodalEvaluator
from src.inference.inference_multimodal import MultimodalInference

# 尝试使用修复版模型
try:
    from src.models.multimodal_yolo_fixed import FixedMultimodalYOLO
    print("✓ 检测到修复版多模态模型")
except ImportError:
    print("⚠️ 使用原始多模态模型")


def demo_data_loading():
    """演示数据加载功能"""
    print("=== 演示数据加载 ===")
    
    try:
        # 创建数据加载器
        train_loader = create_dataloader(
            data_dir="data",
            split='train',
            batch_size=4,
            img_size=640,
            num_workers=2,
            shuffle=True
        )
        
        # 加载一个批次的数据
        for batch in train_loader:
            print(f"✓ RGB图像形状: {batch['rgb'].shape}")
            print(f"✓ 热红外图像形状: {batch['thermal'].shape}")
            print(f"✓ 目标数量: {batch['targets'].shape[0]}")
            print(f"✓ 文件名示例: {batch['filenames'][0]}")
            break
            
        print("数据加载演示完成！\n")
        return True
        
    except Exception as e:
        print(f"✗ 数据加载失败: {e}")
        print("请确保数据集结构正确\n")
        return False


def demo_training(epochs=2):
    """演示模型训练功能"""
    print("=== 演示模型训练 ===")
    
    try:
        # 创建训练配置
        config = create_default_config()
        config['epochs'] = epochs  # 演示用，只训练少量轮次
        config['batch_size'] = 4
        config['output_dir'] = 'runs/demo_train'
        
        print(f"训练配置:")
        print(f"  - 训练轮数: {config['epochs']}")
        print(f"  - 批次大小: {config['batch_size']}")
        print(f"  - 融合类型: {config['model']['fusion_type']}")
        
        # 创建训练器
        trainer = MultimodalTrainer(config)
        
        # 开始训练
        print("开始训练演示...")
        trainer.train()
        
        print("✓ 训练演示完成！")
        print(f"模型权重保存在: {config['output_dir']}/weights/\n")
        return True
        
    except Exception as e:
        print(f"✗ 训练演示失败: {e}\n")
        return False


def demo_evaluation(model_path):
    """演示模型评估功能"""
    print("=== 演示模型评估 ===")
    
    try:
        if not os.path.exists(model_path):
            print(f"✗ 模型文件不存在: {model_path}")
            return False
        
        # 评估配置
        config = {
            'data_dir': 'data',
            'batch_size': 4,
            'img_size': 640,
            'num_workers': 2,
            'model': {
                'fusion_type': 'cross_attention'
            }
        }
        
        # 创建评估器
        evaluator = MultimodalEvaluator(config, model_path)
        
        # 运行评估
        print("开始评估演示...")
        metrics = evaluator.evaluate(conf_thresh=0.25, iou_thresh=0.5)
        
        # 保存结果
        output_dir = 'runs/demo_eval'
        evaluator.save_results(output_dir)
        
        print("✓ 评估演示完成！")
        print(f"评估结果保存在: {output_dir}\n")
        return True
        
    except Exception as e:
        print(f"✗ 评估演示失败: {e}\n")
        return False


def demo_inference(model_path):
    """演示模型推理功能"""
    print("=== 演示模型推理 ===")
    
    try:
        if not os.path.exists(model_path):
            print(f"✗ 模型文件不存在: {model_path}")
            return False
        
        # 推理配置
        config = {
            'img_size': 640,
            'fusion_type': 'cross_attention'
        }
        
        # 创建推理器
        inferencer = MultimodalInference(model_path, config)
        
        # 查找测试图像
        test_dir = Path('data/test/images')
        if not test_dir.exists():
            print(f"✗ 测试图像目录不存在: {test_dir}")
            return False
        
        # 查找第一张测试图像
        image_files = list(test_dir.glob('*.jpg')) + list(test_dir.glob('*.png'))
        if not image_files:
            print(f"✗ 测试目录中没有找到图像文件")
            return False
        
        test_image = str(image_files[0])
        
        # 查找对应的热红外图像
        base_name = image_files[0].stem
        thermal_image = test_dir / 'thermal' / f'{base_name}_thermal.png'
        thermal_path = str(thermal_image) if thermal_image.exists() else None
        
        print(f"测试图像: {test_image}")
        print(f"热红外图像: {thermal_path if thermal_path else '未找到'}")
        
        # 进行推理
        print("开始推理演示...")
        detections = inferencer.predict(
            test_image,
            thermal_path,
            conf_thresh=0.25,
            iou_thresh=0.45
        )
        
        print(f"✓ 检测到 {len(detections)} 个目标")
        for i, det in enumerate(detections):
            print(f"  {i+1}. {det['class_name']}: {det['confidence']:.3f}")
        
        # 可视化结果
        output_dir = 'runs/demo_inference'
        os.makedirs(output_dir, exist_ok=True)
        output_file = os.path.join(output_dir, 'demo_result.jpg')
        
        inferencer.visualize_results(test_image, detections, output_file)
        
        print("✓ 推理演示完成！")
        print(f"结果保存在: {output_file}\n")
        return True
        
    except Exception as e:
        print(f"✗ 推理演示失败: {e}\n")
        return False


def demo_modality_comparison(model_path):
    """演示模态比较功能"""
    print("=== 演示模态比较 ===")
    
    try:
        if not os.path.exists(model_path):
            print(f"✗ 模型文件不存在: {model_path}")
            return False
        
        # 推理配置
        config = {
            'img_size': 640,
            'fusion_type': 'cross_attention'
        }
        
        # 创建推理器
        inferencer = MultimodalInference(model_path, config)
        
        # 查找测试图像
        test_dir = Path('data/test/images')
        if not test_dir.exists():
            print(f"✗ 测试图像目录不存在: {test_dir}")
            return False
        
        image_files = list(test_dir.glob('*.jpg')) + list(test_dir.glob('*.png'))
        if not image_files:
            print(f"✗ 测试目录中没有找到图像文件")
            return False
        
        test_image = str(image_files[0])
        base_name = image_files[0].stem
        thermal_image = test_dir / 'thermal' / f'{base_name}_thermal.png'
        thermal_path = str(thermal_image) if thermal_image.exists() else None
        
        if not thermal_path:
            print("✗ 未找到对应的热红外图像，跳过模态比较演示")
            return False
        
        print(f"比较图像: {test_image}")
        print(f"热红外图像: {thermal_path}")
        
        # 进行模态比较
        print("开始模态比较演示...")
        results = inferencer.compare_modalities(
            test_image,
            thermal_path,
            conf_thresh=0.25,
            iou_thresh=0.45
        )
        
        print("✓ 模态比较结果:")
        print(f"  RGB单独检测: {len(results['rgb_only'])} 个目标")
        print(f"  热红外单独检测: {len(results['thermal_only'])} 个目标")
        print(f"  多模态融合检测: {len(results['multimodal'])} 个目标")
        
        print("✓ 模态比较演示完成！\n")
        return True
        
    except Exception as e:
        print(f"✗ 模态比较演示失败: {e}\n")
        return False


def main():
    parser = argparse.ArgumentParser(description='多模态绝缘子检测系统演示')
    parser.add_argument('--mode', type=str, default='all', 
                       choices=['data', 'train', 'eval', 'inference', 'compare', 'all'],
                       help='演示模式')
    parser.add_argument('--model_path', type=str, default='runs/demo_train/weights/best.pt',
                       help='模型权重路径（用于评估和推理）')
    parser.add_argument('--epochs', type=int, default=2,
                       help='训练演示的轮数')
    
    args = parser.parse_args()
    
    print("🚀 多模态绝缘子检测系统演示")
    print("=" * 50)
    
    # 检查CUDA可用性
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    print(f"CUDA可用: {'是' if torch.cuda.is_available() else '否'}")
    if torch.cuda.is_available():
        print(f"GPU型号: {torch.cuda.get_device_name()}")
    else:
        print("⚠️ 检测到CPU模式，训练速度会较慢")
        print("💡 建议运行: python check_cuda.py 诊断CUDA问题")
        print("💡 如果没有GPU，系统仍可正常运行")
    print()
    
    # 运行演示
    success_count = 0
    total_count = 0
    
    if args.mode in ['data', 'all']:
        total_count += 1
        if demo_data_loading():
            success_count += 1
    
    if args.mode in ['train', 'all']:
        total_count += 1
        if demo_training(args.epochs):
            success_count += 1
    
    if args.mode in ['eval', 'all']:
        total_count += 1
        if demo_evaluation(args.model_path):
            success_count += 1
    
    if args.mode in ['inference', 'all']:
        total_count += 1
        if demo_inference(args.model_path):
            success_count += 1
    
    if args.mode in ['compare', 'all']:
        total_count += 1
        if demo_modality_comparison(args.model_path):
            success_count += 1
    
    # 总结
    print("=" * 50)
    print(f"🎯 演示完成！成功 {success_count}/{total_count} 个模块")
    
    if success_count == total_count:
        print("🎉 所有演示模块都运行成功！")
        print("\n📚 下一步建议:")
        print("1. 使用完整数据集进行正式训练")
        print("2. 调整超参数优化模型性能")
        print("3. 在实际场景中部署和测试模型")
    else:
        print("⚠️ 部分演示模块失败，请检查:")
        print("1. 数据集结构是否正确")
        print("2. 依赖包是否完整安装")
        print("3. 模型文件路径是否正确")
    
    print("\n📖 更多信息请参考 README.md")


if __name__ == '__main__':
    main() 