#!/usr/bin/env python3
"""
模型权重分析脚本
分析训练好的模型权重，检查是否存在问题
"""

import torch
import numpy as np
import sys
import os

def analyze_model_weights(model_path):
    """分析模型权重"""
    print(f"=== 分析模型权重: {model_path} ===")
    
    try:
        # 加载模型权重
        checkpoint = torch.load(model_path, map_location='cpu')
        print(f"权重文件键: {list(checkpoint.keys())}")
        
        if 'model_state_dict' in checkpoint:
            state_dict = checkpoint['model_state_dict']
            print(f"模型状态字典键数量: {len(state_dict)}")
            
            # 检查检测头的权重
            detection_head_keys = [k for k in state_dict.keys() if 'detection_head' in k]
            print(f"检测头相关权重数量: {len(detection_head_keys)}")
            
            # 检查每个检测头的权重
            for i in range(3):  # 3个尺度
                head_keys = [k for k in detection_head_keys if f'heads.{i}' in k]
                print(f"\n检测头{i}的权重: {len(head_keys)}")
                for key in head_keys:
                    weight = state_dict[key]
                    print(f"  {key}: {weight.shape}")
                    if 'bias' in key:
                        # 检查偏置值，特别是类别相关的偏置
                        print(f"    偏置值范围: [{weight.min():.4f}, {weight.max():.4f}]")
                        print(f"    偏置值: {weight[:10]}")  # 显示前10个偏置值
            
            # 检查融合模块权重
            fusion_keys = [k for k in state_dict.keys() if 'fusion' in k]
            print(f"\n融合模块权重数量: {len(fusion_keys)}")
            for key in fusion_keys[:5]:  # 只显示前5个
                weight = state_dict[key]
                print(f"  {key}: {weight.shape}")
        
        # 检查训练历史
        if 'epoch' in checkpoint:
            print(f"\n训练轮数: {checkpoint['epoch']}")
        if 'best_metric' in checkpoint:
            print(f"最佳指标: {checkpoint['best_metric']}")
        if 'optimizer_state_dict' in checkpoint:
            print("包含优化器状态")
        
        return True
        
    except Exception as e:
        print(f"分析模型权重时出错: {e}")
        return False

def analyze_prediction_outputs():
    """分析模型预测输出的问题"""
    print("\n=== 分析预测输出问题 ===")
    
    # 从评估日志中可以看到，模型在评估时有很多边界框修复操作
    # 这表明模型输出的边界框坐标可能存在精度问题
    
    print("观察到的问题:")
    print("1. 评估时大量边界框需要修复 (精度问题)")
    print("2. 4个类别的AP值为0，只有110_two_hight_glass正常")
    print("3. 模型可能存在类别不平衡学习问题")
    
    # 分析数据集分布
    print("\n数据集分布分析:")
    class_names = ['110_two_hight_glass', 'Glass_Dirty', 'Glass_Loss', 'Polyme_Dirty', 'insulator']
    train_counts = [180, 158, 38, 148, 29]
    test_counts = [26, 27, 6, 23, 3]
    
    print("训练集样本数:")
    for i, (name, count) in enumerate(zip(class_names, train_counts)):
        print(f"  {i}: {name}: {count}")
    
    print("测试集样本数:")
    for i, (name, count) in enumerate(zip(class_names, test_counts)):
        print(f"  {i}: {name}: {count}")
    
    # 计算类别比例
    total_train = sum(train_counts)
    print("\n训练集类别比例:")
    for i, (name, count) in enumerate(zip(class_names, train_counts)):
        ratio = count / total_train * 100
        print(f"  {i}: {name}: {ratio:.1f}%")

def main():
    model_path = "runs/train_clean_dataset/20250731_141248/weights/best.pt"
    
    if not os.path.exists(model_path):
        print(f"模型文件不存在: {model_path}")
        return
    
    # 分析模型权重
    analyze_model_weights(model_path)
    
    # 分析预测输出问题
    analyze_prediction_outputs()

if __name__ == '__main__':
    main()
