# 基于清理后数据集的优化训练配置
# 数据集: 553训练+165验证+85测试，5类绝缘子故障检测（高质量单目标）
# 硬件: GPU可用，12核CPU，4GB可用内存

# === 核心训练参数 ===
data_dir: "data"
output_dir: "runs/train_clean_dataset"
nc: 5  # 5个故障类别（删除了无样本的broken disc和pollution-flashover）
img_size: 512  # 平衡精度和内存使用
batch_size: 8   # 数据量减少，可适当增加批次大小
epochs: 120     # 数据量减少，增加训练轮数确保充分学习
num_workers: 4  # Windows系统建议较少进程
grad_clip: 10.0

# === 模型配置 ===
model:
  type: "simple"
  fusion_type: "cross_attention"  # 多模态融合策略

# === 优化器配置 ===
optimizer:
  type: "Adam"
  lr: 0.001      # 初始学习率
  weight_decay: 0.0005  # 防过拟合

# === 学习率调度 ===
scheduler:
  type: "CosineAnnealingLR"  # 余弦退火调度
  T_max: 120     # 与epochs匹配
  eta_min: 0.00001

# === 早停配置 ===
early_stopping:
  patience: 25      # 数据量少，给更多耐心等待收敛
  min_delta: 0.001  # 最小改善阈值，避免微小波动导致误判

# === 高级配置 ===
use_mixed_precision: true   # 混合精度训练（节省GPU内存）
use_ema: true              # 指数移动平均（提升稳定性）

# === 数据增强（针对电力设备优化）===
augmentation:
  brightness_contrast: 0.2
  blur_prob: 0.1
  flip_prob: 0.5
  color_jitter: 0.1

# 说明：
# - batch_size=8: 数据集清理后，可适当增加批次大小提升训练效率
# - epochs=120: 高质量单目标数据，需要更多轮数充分学习特征
# - patience=25: 数据量减少，给模型更多时间收敛
# - 使用混合精度和EMA提升训练效率和稳定性
# - 余弦退火学习率调度，平滑收敛 