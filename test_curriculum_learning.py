#!/usr/bin/env python3
"""
测试课程学习功能
"""

import sys
sys.path.append('.')

from train_minimal_model import MinimalTrainer, create_minimal_config

def test_curriculum_weights():
    """测试课程学习权重计算"""
    print("🧪 测试课程学习权重计算...")
    
    # 创建配置
    config = create_minimal_config()
    
    # 创建课程学习训练器
    trainer = MinimalTrainer(config, training_mode='curriculum')
    
    # 测试不同阶段的权重
    total_epochs = 200
    test_epochs = [10, 50, 100, 150, 200]
    
    print(f"\n📊 课程学习权重变化 (总轮数: {total_epochs}):")
    print("=" * 60)
    
    class_names = ['110_two_hight_glass', 'Glass_Dirty', 'Glass_Loss', 'Polyme_Dirty', 'insulator']
    
    for epoch in test_epochs:
        weights = trainer.get_curriculum_weights(epoch, total_epochs)
        stage_percent = epoch / total_epochs * 100
        
        print(f"\nEpoch {epoch} ({stage_percent:.0f}%):")
        for i, (name, weight) in enumerate(zip(class_names, weights)):
            print(f"  类别{i} ({name}): {weight}")
    
    print("\n✅ 课程学习权重测试完成!")

def test_training_modes():
    """测试不同训练模式"""
    print("\n🧪 测试不同训练模式...")
    
    config = create_minimal_config()
    modes = ['traditional', 'progressive', 'curriculum']
    
    for mode in modes:
        print(f"\n📚 测试 {mode} 模式:")
        try:
            trainer = MinimalTrainer(config, training_mode=mode)
            print(f"  ✅ {mode} 模式创建成功")
            
            # 测试权重计算
            if mode == 'curriculum':
                weights = trainer.get_curriculum_weights(50, 200)
                print(f"  📊 第50轮权重: {weights}")
            
        except Exception as e:
            print(f"  ❌ {mode} 模式失败: {e}")
    
    print("\n✅ 训练模式测试完成!")

if __name__ == '__main__':
    print("🚀 课程学习功能测试")
    print("=" * 50)
    
    test_curriculum_weights()
    test_training_modes()
    
    print("\n🎉 所有测试完成!")
    print("\n💡 使用建议:")
    print("  1. 对于当前数据集，推荐使用课程学习模式")
    print("  2. 执行命令: python train_minimal_model.py --mode curriculum")
    print("  3. 课程学习会在4个阶段动态调整类别权重")
    print("  4. 特别适合解决Glass_Loss和insulator类别的检测问题")
