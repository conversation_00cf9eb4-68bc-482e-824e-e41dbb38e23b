#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
模型输出调试脚本
用于诊断为什么评估指标为0
"""

import os
import sys
import torch
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from src.models.multimodal_yolo_fixed import FixedMultimodalYOLO


def debug_model_checkpoint(model_path):
    """调试模型检查点信息"""
    print("🔍 [DEBUG] 开始模型检查点调试...")

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")

    # 1. 检查检查点内容
    print(f"\n📁 [DEBUG] 加载检查点: {model_path}")
    checkpoint = torch.load(model_path, map_location=device)

    print(f"📊 [DEBUG] 检查点键: {list(checkpoint.keys())}")

    if 'epoch' in checkpoint:
        print(f"📊 [DEBUG] 训练轮数: {checkpoint['epoch']}")
    if 'best_val_loss' in checkpoint:
        print(f"📊 [DEBUG] 最佳验证损失: {checkpoint['best_val_loss']:.4f}")
    if 'train_losses' in checkpoint:
        train_losses = checkpoint['train_losses']
        print(f"📊 [DEBUG] 训练损失历史长度: {len(train_losses)}")
        if len(train_losses) > 0:
            print(f"📊 [DEBUG] 最后训练损失: {train_losses[-1]:.4f}")
    if 'val_losses' in checkpoint:
        val_losses = checkpoint['val_losses']
        print(f"📊 [DEBUG] 验证损失历史长度: {len(val_losses)}")
        if len(val_losses) > 0:
            print(f"📊 [DEBUG] 最后验证损失: {val_losses[-1]:.4f}")

    # 2. 检查模型权重
    print(f"\n🔍 [DEBUG] 检查模型权重...")
    if 'model_state_dict' in checkpoint:
        state_dict = checkpoint['model_state_dict']
    else:
        state_dict = checkpoint

    print(f"📊 [DEBUG] 模型权重键数量: {len(state_dict.keys())}")

    # 检查检测头权重
    detection_head_keys = [k for k in state_dict.keys() if 'detect' in k.lower() or 'head' in k.lower()]
    print(f"📊 [DEBUG] 检测头相关权重: {len(detection_head_keys)}")

    for key in detection_head_keys[:5]:  # 只显示前5个
        weight = state_dict[key]
        print(f"  🔧 [DEBUG] {key}: 形状={weight.shape}, 范围=[{weight.min():.4f}, {weight.max():.4f}]")

    # 检查置信度偏置
    conf_bias_keys = [k for k in state_dict.keys() if 'bias' in k and ('conf' in k or 'obj' in k)]
    if not conf_bias_keys:
        # 查找可能的置信度偏置
        conf_bias_keys = [k for k in state_dict.keys() if 'bias' in k and 'detect' in k]

    print(f"📊 [DEBUG] 置信度偏置相关权重: {len(conf_bias_keys)}")
    for key in conf_bias_keys:
        bias = state_dict[key]
        print(f"  🎯 [DEBUG] {key}: 形状={bias.shape}, 值={bias.cpu().numpy()}")

    # 3. 创建模型并测试简单输入
    print(f"\n🔍 [DEBUG] 测试模型推理...")
    model = FixedMultimodalYOLO(nc=5, fusion_type='cross_attention').to(device)

    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
    else:
        model.load_state_dict(checkpoint)

    model.eval()

    # 创建虚拟输入
    with torch.no_grad():
        batch_size = 1
        rgb_input = torch.randn(batch_size, 3, 512, 512).to(device)
        thermal_input = torch.randn(batch_size, 3, 512, 512).to(device)

        print(f"🖼️ [DEBUG] 虚拟输入形状: RGB={rgb_input.shape}, Thermal={thermal_input.shape}")

        # 模型推理
        outputs = model(rgb_input, thermal_input)

        print(f"📤 [DEBUG] 模型输出数量: {len(outputs)}")

        for j, output in enumerate(outputs):
            print(f"📤 [DEBUG] 输出{j}形状: {output.shape}")
            if output.numel() > 0:
                print(f"📤 [DEBUG] 输出{j}数值范围: [{output.min():.4f}, {output.max():.4f}]")

                # 分析置信度
                if output.shape[-1] >= 10:  # 确保有足够的维度
                    pred = output[0]  # 第一个batch
                    if pred.shape[0] > 0:
                        obj_conf_raw = pred[:, 4]  # 原始对象置信度
                        obj_conf = torch.sigmoid(obj_conf_raw)

                        print(f"  🎯 [DEBUG] 原始对象置信度范围: [{obj_conf_raw.min():.4f}, {obj_conf_raw.max():.4f}]")
                        print(f"  🎯 [DEBUG] Sigmoid对象置信度范围: [{obj_conf.min():.4f}, {obj_conf.max():.4f}]")
                        print(f"  🎯 [DEBUG] 最高对象置信度: {obj_conf.max():.4f}")

    print(f"\n🎯 [CONCLUSION] 调试结论:")
    print("1. 检查置信度偏置是否正确设置（应该约为-4.6）")
    print("2. 检查对象置信度是否过低（Sigmoid后应该有一些>0.01的值）")
    print("3. 如果所有置信度都很低，可能需要降低评估阈值")
    print("4. 建议使用置信度阈值0.001-0.01进行评估")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='模型输出调试')
    parser.add_argument('--model_path', type=str, required=True, help='模型权重路径')
    parser.add_argument('--data_dir', type=str, default='data', help='数据集目录')
    
    args = parser.parse_args()
    
    debug_model_checkpoint(args.model_path)
