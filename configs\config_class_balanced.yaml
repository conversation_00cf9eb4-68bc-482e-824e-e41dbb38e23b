# 针对类别不平衡问题的激进平衡配置
# 基于分析结果：Glass_Loss(38样本)、insulator(29样本)严重不足

# === 核心训练参数 ===
data_dir: "data"
output_dir: "runs/train_class_balanced"
nc: 5
img_size: 512
batch_size: 4  # 减小批次，让少数类别有更多机会
epochs: 150    # 增加训练轮数
num_workers: 0
grad_clip: 5.0

# === 模型配置 ===
model:
  type: "simple"
  fusion_type: "cross_attention"

# === 激进的类别权重策略 ===
class_weights: [1.0, 2.0, 8.0, 3.0, 10.0]  # 对应5个类别的权重
# 110_two_hight_glass: 1.0 (基准)
# Glass_Dirty: 2.0 (轻微增强)  
# Glass_Loss: 8.0 (激进增强，样本最少)
# Polyme_Dirty: 3.0 (中等增强)
# insulator: 10.0 (最激进增强，样本极少)

# === 优化器配置 ===
optimizer:
  type: "Adam"
  lr: 0.0001  # 降低学习率，让模型更仔细学习少数类别
  weight_decay: 0.0005

# === 学习率调度 ===
scheduler:
  type: "CosineAnnealingLR"
  T_max: 150
  eta_min: 1e-7

# === 早停配置 ===
early_stopping:
  patience: 30  # 增加耐心
  min_delta: 0.001

# === Focal Loss配置 ===
loss_config:
  use_focal_loss: true
  focal_alpha: 0.25
  focal_gamma: 2.0
  
# === 损失函数权重 ===
loss_weights:
  box_loss: 1.0
  obj_loss: 3.0    # 增加置信度损失权重
  cls_loss: 2.0    # 增加分类损失权重

# === 评估配置 ===
evaluation:
  conf_thresh: 0.05  # 大幅降低置信度阈值
  iou_thresh: 0.5
  save_best: true

# === 数据增强（针对少数类别优化）===
augmentation:
  brightness_contrast: 0.4  # 增加亮度对比度变化
  blur_prob: 0.2
  flip_prob: 0.5
  color_jitter: 0.3
  rotate_prob: 0.15
  scale_range: [0.7, 1.3]
  mixup_prob: 0.1           # 添加mixup增强
  cutmix_prob: 0.1          # 添加cutmix增强

# === 高级配置 ===
use_mixed_precision: true
use_ema: true

# === 类别特定增强 ===
class_specific_augmentation:
  # 对少数类别进行额外增强
  Glass_Loss:
    extra_augment_prob: 0.8
    copy_paste_prob: 0.3
  insulator:
    extra_augment_prob: 0.8
    copy_paste_prob: 0.3

# === 采样策略 ===
sampling_strategy:
  use_balanced_sampling: true
  oversample_minority: true
  minority_oversample_ratio: 3.0  # 少数类别过采样3倍

# === 日志配置 ===
logging:
  log_interval: 5
  save_interval: 10
  class_specific_metrics: true  # 记录每个类别的详细指标
