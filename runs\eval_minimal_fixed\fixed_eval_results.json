{"mAP": 0.3, "AP_per_class": {"110_two_hight_glass": 0.5, "Glass_Dirty": 0.5, "Glass_Loss": 0.0, "Polyme_Dirty": 0.5, "insulator": 0.0}, "total_predictions": 1620, "valid_predictions": 1620, "samples_processed": 10, "gt_per_class": {"110_two_hight_glass": 2, "Glass_Dirty": 6, "Glass_Loss": 0, "Polyme_Dirty": 2, "insulator": 0}, "conf_thresh": 0.001, "model_type": "minimal", "note": "这是基于模型输出的快速评估，不是完整的mAP计算"}