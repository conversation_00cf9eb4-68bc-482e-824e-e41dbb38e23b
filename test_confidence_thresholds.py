#!/usr/bin/env python3
"""
置信度阈值测试脚本
测试不同置信度阈值对评估结果的影响，找到最佳阈值
"""

import os
import sys
import numpy as np

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)
sys.path.insert(0, current_dir)

# 导入评估模块
from src.training.evaluate_multimodal import MultimodalEvaluator, load_config

def test_confidence_thresholds():
    """测试不同置信度阈值的效果"""
    print("🔍 [THRESHOLD TEST] 开始测试置信度阈值...")
    
    # 模型路径
    model_path = "runs/train_clean_dataset/20250731_141248/weights/best.pt"
    
    # 基础配置
    config = {
        'data_dir': 'data',
        'batch_size': 16,
        'img_size': 640,
        'num_workers': 0,
        'model': {
            'fusion_type': 'cross_attention'
        }
    }
    
    # 测试的置信度阈值范围
    conf_thresholds = [0.001, 0.005, 0.01, 0.02, 0.05, 0.1, 0.15, 0.2, 0.25, 0.3]
    iou_thresh = 0.45  # 固定IoU阈值
    
    print(f"📊 测试置信度阈值: {conf_thresholds}")
    print(f"🎯 固定IoU阈值: {iou_thresh}")
    print("-" * 80)
    
    results = []
    
    # 创建评估器
    evaluator = MultimodalEvaluator(config, model_path)
    
    for conf_thresh in conf_thresholds:
        print(f"\n🔄 测试置信度阈值: {conf_thresh}")
        
        try:
            # 进行评估
            metrics = evaluator.evaluate(conf_thresh=conf_thresh, iou_thresh=iou_thresh)
            
            # 记录结果
            result = {
                'conf_thresh': conf_thresh,
                'mAP': metrics['mAP'],
                'overall_precision': metrics['overall_precision'],
                'overall_recall': metrics['overall_recall'],
                'F1': metrics['F1'],
                'AP_per_class': metrics['AP_per_class'].copy()
            }
            results.append(result)
            
            # 显示关键指标
            print(f"  mAP: {metrics['mAP']:.4f}")
            print(f"  精确率: {metrics['overall_precision']:.4f}")
            print(f"  召回率: {metrics['overall_recall']:.4f}")
            
            # 显示各类别AP
            working_classes = [name for name, ap in metrics['AP_per_class'].items() if ap > 0]
            if working_classes:
                print(f"  有效类别: {working_classes}")
            else:
                print(f"  有效类别: 无")
                
        except Exception as e:
            print(f"  ❌ 评估失败: {e}")
            continue
    
    # 分析结果
    print("\n" + "=" * 80)
    print("📈 [ANALYSIS] 阈值测试结果分析")
    print("=" * 80)
    
    if not results:
        print("❌ 没有成功的评估结果")
        return
    
    # 找到最佳阈值
    best_map_result = max(results, key=lambda x: x['mAP'])
    best_f1_result = max(results, key=lambda x: x['F1'])
    
    print(f"\n🏆 最佳mAP结果:")
    print(f"  置信度阈值: {best_map_result['conf_thresh']}")
    print(f"  mAP: {best_map_result['mAP']:.4f}")
    print(f"  精确率: {best_map_result['overall_precision']:.4f}")
    print(f"  召回率: {best_map_result['overall_recall']:.4f}")
    
    print(f"\n🏆 最佳F1结果:")
    print(f"  置信度阈值: {best_f1_result['conf_thresh']}")
    print(f"  F1分数: {best_f1_result['F1']:.4f}")
    print(f"  mAP: {best_f1_result['mAP']:.4f}")
    
    # 详细结果表格
    print(f"\n📊 详细结果表格:")
    print(f"{'阈值':<8} {'mAP':<8} {'精确率':<8} {'召回率':<8} {'F1':<8} {'有效类别数':<10}")
    print("-" * 60)
    
    for result in results:
        working_count = sum(1 for ap in result['AP_per_class'].values() if ap > 0)
        print(f"{result['conf_thresh']:<8.3f} {result['mAP']:<8.4f} {result['overall_precision']:<8.4f} "
              f"{result['overall_recall']:<8.4f} {result['F1']:<8.4f} {working_count:<10}")
    
    # 推荐阈值
    print(f"\n💡 [RECOMMENDATION] 推荐配置:")
    
    # 如果最佳mAP > 0，推荐该阈值
    if best_map_result['mAP'] > 0:
        print(f"  推荐置信度阈值: {best_map_result['conf_thresh']}")
        print(f"  预期mAP: {best_map_result['mAP']:.4f}")
        
        # 显示该阈值下各类别表现
        print(f"\n  各类别表现:")
        for class_name, ap in best_map_result['AP_per_class'].items():
            status = "✅" if ap > 0.1 else "⚠️" if ap > 0 else "❌"
            print(f"    {status} {class_name}: {ap:.4f}")
    else:
        print(f"  ⚠️ 所有阈值下mAP都为0，模型可能需要重新训练")
        print(f"  建议检查:")
        print(f"    1. 模型权重是否正确加载")
        print(f"    2. 数据集标注是否正确")
        print(f"    3. 模型架构是否匹配")
        print(f"    4. 训练过程是否收敛")

if __name__ == '__main__':
    test_confidence_thresholds()
