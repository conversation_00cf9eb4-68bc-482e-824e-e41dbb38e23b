#!/usr/bin/env python3
"""
测试模型输出脚本 - 验证模型是否能正常预测
"""

import os
import sys
import torch
import numpy as np
import cv2

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)
sys.path.insert(0, current_dir)

from src.models.multimodal_yolo_fixed import FixedMultimodalYOLO

def load_and_test_model(model_path, test_image_path):
    """加载模型并测试单张图像"""
    print(f"🔧 加载模型: {model_path}")
    
    # 创建模型
    model = FixedMultimodalYOLO(
        nc=5,
        fusion_type='cross_attention'
    )
    
    # 加载权重
    checkpoint = torch.load(model_path, map_location='cpu')

    # 处理不同的权重格式
    if 'model_state_dict' in checkpoint:
        model_weights = checkpoint['model_state_dict']
    elif 'model' in checkpoint:
        model_weights = checkpoint['model']
    else:
        model_weights = checkpoint

    # 尝试加载权重，允许部分匹配
    try:
        model.load_state_dict(model_weights, strict=False)
        print("✅ 权重加载成功（允许部分匹配）")
    except Exception as e:
        print(f"⚠️ 权重加载失败，尝试使用训练时的模型架构: {e}")
        # 如果权重不匹配，说明需要使用训练时的实际模型类
        return None
    
    model.eval()
    print("✅ 模型加载成功")
    
    # 加载测试图像
    print(f"🖼️ 加载测试图像: {test_image_path}")
    
    # RGB图像
    rgb_img = cv2.imread(test_image_path)
    rgb_img = cv2.cvtColor(rgb_img, cv2.COLOR_BGR2RGB)
    rgb_img = cv2.resize(rgb_img, (640, 640))
    rgb_tensor = torch.from_numpy(rgb_img).permute(2, 0, 1).float() / 255.0
    rgb_tensor = rgb_tensor.unsqueeze(0)
    
    # 热红外图像
    thermal_path = test_image_path.replace('.jpg', '_thermal.png').replace('images', 'images/thermal')
    if os.path.exists(thermal_path):
        thermal_img = cv2.imread(thermal_path, cv2.IMREAD_GRAYSCALE)
        thermal_img = cv2.cvtColor(thermal_img, cv2.COLOR_GRAY2RGB)
    else:
        thermal_img = np.zeros_like(rgb_img)
    
    thermal_img = cv2.resize(thermal_img, (640, 640))
    thermal_tensor = torch.from_numpy(thermal_img).permute(2, 0, 1).float() / 255.0
    thermal_tensor = thermal_tensor.unsqueeze(0)
    
    print("✅ 图像预处理完成")
    
    # 模型推理
    with torch.no_grad():
        outputs = model(rgb_tensor, thermal_tensor)
    
    print(f"📊 模型输出分析:")
    print(f"  输出数量: {len(outputs)}")
    
    total_detections = 0
    for i, output in enumerate(outputs):
        print(f"  输出{i} 形状: {output.shape}")
        print(f"  输出{i} 数值范围: [{output.min().item():.4f}, {output.max().item():.4f}]")
        
        # 分析置信度
        if len(output.shape) == 4:  # [B, C, H, W]
            b, c, h, w = output.shape
            # 重塑为 [B, H*W, C]
            output_reshaped = output.permute(0, 2, 3, 1).reshape(b, h*w, c)
            
            # 提取置信度（第5个通道，索引4）
            if c > 4:
                conf_raw = output_reshaped[0, :, 4]  # 原始置信度logits
                conf_sigmoid = torch.sigmoid(conf_raw)  # 应用sigmoid
                
                print(f"    原始置信度范围: [{conf_raw.min().item():.4f}, {conf_raw.max().item():.4f}]")
                print(f"    Sigmoid置信度范围: [{conf_sigmoid.min().item():.6f}, {conf_sigmoid.max().item():.6f}]")
                print(f"    Sigmoid置信度均值: {conf_sigmoid.mean().item():.6f}")
                
                # 统计不同阈值下的检测数
                thresholds = [0.1, 0.05, 0.01, 0.005, 0.001]
                for thresh in thresholds:
                    count = (conf_sigmoid > thresh).sum().item()
                    print(f"    置信度 > {thresh}: {count}/{len(conf_sigmoid)} 个检测")
                    total_detections += count
    
    print(f"\n🎯 总结:")
    print(f"  模型能够正常推理: ✅")
    print(f"  输出格式正确: ✅")
    if total_detections > 0:
        print(f"  检测到目标: ✅ (在低阈值下)")
        print(f"  建议使用更低的置信度阈值进行评估")
    else:
        print(f"  未检测到目标: ❌")
        print(f"  可能需要检查模型训练或数据预处理")

def main():
    model_path = "runs/train_progressive/20250802_170243/stage3/weights/best.pt"
    test_image = "data/test/images/100_PNG_jpg.rf.3511803ad7b07648ab43e7534941560f.jpg"
    
    print("🔍 模型输出测试开始...")
    print("-" * 60)
    
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return
    
    if not os.path.exists(test_image):
        print(f"❌ 测试图像不存在: {test_image}")
        return
    
    try:
        load_and_test_model(model_path, test_image)
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
