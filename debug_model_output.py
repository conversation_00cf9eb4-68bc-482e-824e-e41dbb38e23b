#!/usr/bin/env python3
"""
调试模型输出脚本
检查模型的实际输出值和分布
"""

import os
import sys
import torch
import glob

# 添加路径
sys.path.append('.')
sys.path.append('src')

def load_minimal_model(model_path, device='cuda'):
    """加载极简模型"""
    from src.models.minimal_multimodal import MinimalMultimodalYOLO
    
    model = MinimalMultimodalYOLO(nc=5)
    checkpoint = torch.load(model_path, map_location='cpu', weights_only=False)
    
    if 'model_state_dict' in checkpoint:
        state_dict = checkpoint['model_state_dict']
    else:
        state_dict = checkpoint
    
    model.load_state_dict(state_dict)
    model = model.to(device)
    model.eval()
    
    return model

def load_image_pair(image_path, img_size=640):
    """加载RGB和热红外图像对"""
    try:
        from PIL import Image
        import torchvision.transforms as transforms
        
        # 加载RGB图像
        rgb_img = Image.open(image_path).convert('RGB')
        
        # 查找对应的热红外图像
        thermal_path = image_path.replace('images', 'thermal')
        if thermal_path.endswith('.jpg'):
            thermal_path = thermal_path.replace('.jpg', '.png')
        
        if os.path.exists(thermal_path):
            thermal_img = Image.open(thermal_path).convert('L')
        else:
            thermal_img = rgb_img.convert('L')
        
        # 转换为tensor
        transform = transforms.Compose([
            transforms.Resize((img_size, img_size)),
            transforms.ToTensor()
        ])
        
        rgb_tensor = transform(rgb_img).unsqueeze(0)
        thermal_tensor = transform(thermal_img).unsqueeze(0)
        
        return rgb_tensor, thermal_tensor
        
    except Exception as e:
        print(f"图像加载失败，使用随机输入: {e}")
        # 使用随机输入
        rgb_tensor = torch.rand(1, 3, img_size, img_size)
        thermal_tensor = torch.rand(1, 1, img_size, img_size)
        return rgb_tensor, thermal_tensor

def debug_model_output(model_path, data_dir='data', device='cuda'):
    """调试模型输出"""
    print("🔍 [DEBUG] 开始调试模型输出...")
    
    # 加载模型
    model = load_minimal_model(model_path, device)
    print("✅ [SUCCESS] 模型加载成功")
    
    # 获取一张测试图像
    test_images_dir = os.path.join(data_dir, 'test', 'images')
    image_files = glob.glob(os.path.join(test_images_dir, '*.jpg')) + \
                  glob.glob(os.path.join(test_images_dir, '*.png'))
    
    if not image_files:
        print("❌ [ERROR] 没有找到测试图像")
        return
    
    # 使用第一张图像进行调试
    image_file = image_files[0]
    image_name = os.path.basename(image_file)
    print(f"📊 [DEBUG] 使用图像: {image_name}")
    
    # 加载图像
    rgb_tensor, thermal_tensor = load_image_pair(image_file)
    rgb_tensor = rgb_tensor.to(device)
    thermal_tensor = thermal_tensor.to(device)
    
    print(f"📊 [INPUT] RGB shape: {rgb_tensor.shape}")
    print(f"📊 [INPUT] Thermal shape: {thermal_tensor.shape}")
    print(f"📊 [INPUT] RGB range: [{rgb_tensor.min():.4f}, {rgb_tensor.max():.4f}]")
    print(f"📊 [INPUT] Thermal range: [{thermal_tensor.min():.4f}, {thermal_tensor.max():.4f}]")
    
    # 模型推理
    with torch.no_grad():
        try:
            outputs = model(rgb_tensor, thermal_tensor)
            
            print(f"\n📊 [OUTPUT] 模型输出:")
            print(f"  输出数量: {len(outputs)}")
            
            for i, output in enumerate(outputs):
                print(f"  输出 {i}: shape={output.shape}")
                print(f"    数据类型: {output.dtype}")
                print(f"    设备: {output.device}")
                print(f"    值范围: [{output.min():.6f}, {output.max():.6f}]")
                print(f"    均值: {output.mean():.6f}")
                print(f"    标准差: {output.std():.6f}")
                
                # 检查是否有NaN或Inf
                if torch.isnan(output).any():
                    print(f"    ⚠️ 包含NaN值!")
                if torch.isinf(output).any():
                    print(f"    ⚠️ 包含Inf值!")
                
                # 分析输出的各个通道
                if len(output.shape) == 4:  # [batch, channels, height, width]
                    batch_size, channels, height, width = output.shape
                    print(f"    详细分析 [B={batch_size}, C={channels}, H={height}, W={width}]:")
                    
                    # 分析每个通道
                    for c in range(min(channels, 10)):  # 最多显示前10个通道
                        channel_data = output[0, c, :, :]
                        print(f"      通道 {c}: 范围[{channel_data.min():.6f}, {channel_data.max():.6f}], 均值={channel_data.mean():.6f}")
                    
                    # 特别分析置信度通道（通道4）
                    if channels > 4:
                        obj_conf_raw = output[0, 4, :, :]
                        obj_conf_sigmoid = torch.sigmoid(obj_conf_raw)
                        print(f"    🎯 置信度分析:")
                        print(f"      原始值范围: [{obj_conf_raw.min():.6f}, {obj_conf_raw.max():.6f}]")
                        print(f"      Sigmoid后范围: [{obj_conf_sigmoid.min():.6f}, {obj_conf_sigmoid.max():.6f}]")
                        print(f"      Sigmoid后均值: {obj_conf_sigmoid.mean():.6f}")
                        print(f"      > 0.1的比例: {(obj_conf_sigmoid > 0.1).float().mean():.4f}")
                        print(f"      > 0.05的比例: {(obj_conf_sigmoid > 0.05).float().mean():.4f}")
                        print(f"      > 0.01的比例: {(obj_conf_sigmoid > 0.01).float().mean():.4f}")
                        print(f"      最大置信度位置: {torch.unravel_index(obj_conf_sigmoid.argmax(), obj_conf_sigmoid.shape)}")
                    
                    # 分析类别预测（通道5-9）
                    if channels >= 10:
                        class_logits = output[0, 5:, :, :]
                        class_probs = torch.softmax(class_logits, dim=0)
                        print(f"    🏷️ 类别分析:")
                        for cls_id in range(5):
                            cls_prob = class_probs[cls_id, :, :]
                            print(f"      类别 {cls_id}: 最大概率={cls_prob.max():.6f}, 均值={cls_prob.mean():.6f}")
            
            # 尝试解析输出
            print(f"\n🔧 [PARSE] 尝试解析输出...")
            
            if outputs and len(outputs) > 0:
                output = outputs[0]
                batch_size, channels, height, width = output.shape
                
                detection_count = 0
                max_conf = 0.0
                
                for h in range(height):
                    for w in range(width):
                        pred = output[0, :, h, w]
                        
                        # 解析预测
                        obj_conf_raw = pred[4]
                        obj_conf = torch.sigmoid(obj_conf_raw)
                        
                        max_conf = max(max_conf, obj_conf.item())
                        
                        if obj_conf > 0.001:  # 极低阈值
                            detection_count += 1
                            
                            if detection_count <= 5:  # 只显示前5个检测
                                class_logits = pred[5:]
                                class_probs = torch.softmax(class_logits, dim=0)
                                class_conf, class_id = class_probs.max(dim=0)
                                final_conf = obj_conf * class_conf
                                
                                print(f"    检测 {detection_count}: 位置({h},{w}), obj_conf={obj_conf:.6f}, class_id={class_id.item()}, final_conf={final_conf:.6f}")
                
                print(f"    总检测数 (>0.001): {detection_count}")
                print(f"    最大置信度: {max_conf:.6f}")
                
        except Exception as e:
            print(f"❌ [ERROR] 推理失败: {e}")
            import traceback
            traceback.print_exc()

def main():
    model_path = "runs/train_minimal_curriculum/20250804_132418/weights/best.pt"
    data_dir = "data"
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    print(f"🖥️ [DEVICE] 使用设备: {device}")
    debug_model_output(model_path, data_dir, device)

if __name__ == '__main__':
    main()
