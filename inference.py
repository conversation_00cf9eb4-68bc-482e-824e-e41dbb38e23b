#!/usr/bin/env python3
"""
多模态绝缘子检测推理入口脚本
"""

import os
import sys

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)
sys.path.insert(0, current_dir)

# 导入推理模块
from src.inference.inference_multimodal import main

if __name__ == '__main__':
    print("[INFERENCE] 启动多模态绝缘子检测推理...")
    print("[INFO] 项目根目录:", current_dir)
    print("-" * 50)
    
    # 运行推理
    main() 