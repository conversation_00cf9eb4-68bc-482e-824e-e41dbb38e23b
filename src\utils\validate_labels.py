#!/usr/bin/env python3
"""
标注文件验证和修复脚本
检查YOLO格式标注文件中的坐标是否在有效范围内
"""

import os

# 添加src目录到Python路径
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import glob
import argparse
from pathlib import Path

def validate_yolo_bbox(bbox):
    """验证YOLO格式边界框坐标"""
    x_center, y_center, width, height = bbox
    
    # 检查坐标范围
    issues = []
    if not (0 <= x_center <= 1):
        issues.append(f"x_center={x_center} 超出范围[0,1]")
    if not (0 <= y_center <= 1):
        issues.append(f"y_center={y_center} 超出范围[0,1]")
    if not (0 <= width <= 1):
        issues.append(f"width={width} 超出范围[0,1]")
    if not (0 <= height <= 1):
        issues.append(f"height={height} 超出范围[0,1]")
    
    # 检查边界框边界
    x_min = x_center - width / 2
    y_min = y_center - height / 2
    x_max = x_center + width / 2
    y_max = y_center + height / 2
    
    if x_min < 0:
        issues.append(f"x_min={x_min} < 0")
    if y_min < 0:
        issues.append(f"y_min={y_min} < 0")
    if x_max > 1:
        issues.append(f"x_max={x_max} > 1")
    if y_max > 1:
        issues.append(f"y_max={y_max} > 1")
    
    return issues

def fix_yolo_bbox(bbox):
    """修复YOLO格式边界框坐标"""
    x_center, y_center, width, height = bbox
    
    # 修复坐标范围
    x_center = max(0.0, min(1.0, x_center))
    y_center = max(0.0, min(1.0, y_center))
    width = max(0.0, min(1.0, width))
    height = max(0.0, min(1.0, height))
    
    # 确保边界框不超出图像边界
    x_min = x_center - width / 2
    y_min = y_center - height / 2
    x_max = x_center + width / 2
    y_max = y_center + height / 2
    
    if x_min < 0:
        x_center = width / 2
    if y_min < 0:
        y_center = height / 2
    if x_max > 1:
        x_center = 1 - width / 2
    if y_max > 1:
        y_center = 1 - height / 2
    
    # 确保width和height为正值
    width = max(0.001, width)  # 最小宽度
    height = max(0.001, height)  # 最小高度
    
    return [x_center, y_center, width, height]

def validate_label_file(label_path, fix_errors=False):
    """验证单个标注文件"""
    issues = []
    fixed_lines = []
    
    try:
        with open(label_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
            
            parts = line.split()
            if len(parts) < 5:
                issue = f"第{line_num}行: 格式错误，应为 'class_id x_center y_center width height'"
                issues.append(issue)
                if fix_errors:
                    continue  # 跳过格式错误的行
                else:
                    fixed_lines.append(line)
                continue
            
            try:
                class_id = int(parts[0])
                x_center = float(parts[1])
                y_center = float(parts[2])
                width = float(parts[3])
                height = float(parts[4])
                
                bbox = [x_center, y_center, width, height]
                bbox_issues = validate_yolo_bbox(bbox)
                
                if bbox_issues:
                    issue = f"第{line_num}行: {', '.join(bbox_issues)}"
                    issues.append(issue)
                    
                    if fix_errors:
                        fixed_bbox = fix_yolo_bbox(bbox)
                        fixed_line = f"{class_id} {fixed_bbox[0]:.6f} {fixed_bbox[1]:.6f} {fixed_bbox[2]:.6f} {fixed_bbox[3]:.6f}"
                        fixed_lines.append(fixed_line)
                        print(f"修复: {label_path} 第{line_num}行")
                        print(f"  原始: {line}")
                        print(f"  修复: {fixed_line}")
                    else:
                        fixed_lines.append(line)
                else:
                    fixed_lines.append(line)
                    
            except ValueError as e:
                issue = f"第{line_num}行: 数值解析错误 - {e}"
                issues.append(issue)
                if not fix_errors:
                    fixed_lines.append(line)
    
    except Exception as e:
        issues.append(f"文件读取错误: {e}")
        return issues, []
    
    return issues, fixed_lines

def validate_dataset(data_dir, splits=['train', 'valid', 'test'], fix_errors=False):
    """验证整个数据集的标注文件"""
    print(f"🔍 验证数据集标注文件: {data_dir}")
    print(f"修复模式: {'开启' if fix_errors else '关闭'}")
    print("=" * 60)
    
    total_files = 0
    problem_files = 0
    total_issues = 0
    
    for split in splits:
        labels_dir = os.path.join(data_dir, split, 'labels')
        if not os.path.exists(labels_dir):
            print(f"⚠️ 标签目录不存在: {labels_dir}")
            continue
        
        print(f"\n📁 检查 {split} 数据集...")
        label_files = glob.glob(os.path.join(labels_dir, '*.txt'))
        
        if not label_files:
            print(f"⚠️ 没有找到标注文件: {labels_dir}")
            continue
        
        split_problem_files = 0
        split_issues = 0
        
        for label_file in label_files:
            total_files += 1
            issues, fixed_lines = validate_label_file(label_file, fix_errors)
            
            if issues:
                problem_files += 1
                split_problem_files += 1
                total_issues += len(issues)
                split_issues += len(issues)
                
                print(f"\n❌ {os.path.basename(label_file)}:")
                for issue in issues:
                    print(f"  {issue}")
                
                # 如果开启修复模式，保存修复后的文件
                if fix_errors and fixed_lines:
                    backup_file = label_file + '.backup'
                    if not os.path.exists(backup_file):
                        # 备份原文件
                        with open(label_file, 'r', encoding='utf-8') as f:
                            original_content = f.read()
                        with open(backup_file, 'w', encoding='utf-8') as f:
                            f.write(original_content)
                        print(f"  📋 已备份原文件: {backup_file}")
                    
                    # 保存修复后的文件
                    with open(label_file, 'w', encoding='utf-8') as f:
                        for line in fixed_lines:
                            f.write(line + '\n')
                    print(f"  ✅ 已修复并保存")
        
        print(f"\n📊 {split} 数据集统计:")
        print(f"  总文件数: {len(label_files)}")
        print(f"  问题文件: {split_problem_files}")
        print(f"  问题总数: {split_issues}")
    
    # 总结
    print("\n" + "=" * 60)
    print("📈 整体统计:")
    print(f"  总标注文件: {total_files}")
    print(f"  问题文件数: {problem_files}")
    print(f"  问题总数: {total_issues}")
    
    if total_issues == 0:
        print("🎉 所有标注文件都正常！")
    elif fix_errors:
        print("✅ 所有问题已修复！原文件已备份为 .backup")
        print("💡 现在可以重新运行训练了")
    else:
        print("⚠️ 发现标注问题，建议运行修复:")
        print("python validate_labels.py --data_dir . --fix")

def main():
    parser = argparse.ArgumentParser(description='验证和修复YOLO格式标注文件')
    parser.add_argument('--data_dir', type=str, default='.', help='数据集根目录')
    parser.add_argument('--splits', nargs='+', default=['train', 'valid', 'test'], help='要检查的数据分割')
    parser.add_argument('--fix', action='store_true', help='自动修复发现的问题')
    parser.add_argument('--file', type=str, help='验证单个标注文件')
    
    args = parser.parse_args()
    
    if args.file:
        # 验证单个文件
        print(f"🔍 验证单个文件: {args.file}")
        issues, fixed_lines = validate_label_file(args.file, args.fix)
        
        if issues:
            print("❌ 发现问题:")
            for issue in issues:
                print(f"  {issue}")
            
            if args.fix:
                backup_file = args.file + '.backup'
                if not os.path.exists(backup_file):
                    os.rename(args.file, backup_file)
                    print(f"📋 已备份原文件: {backup_file}")
                
                with open(args.file, 'w', encoding='utf-8') as f:
                    for line in fixed_lines:
                        f.write(line + '\n')
                print("✅ 文件已修复")
        else:
            print("✅ 文件正常")
    else:
        # 验证整个数据集
        validate_dataset(args.data_dir, args.splits, args.fix)

if __name__ == '__main__':
    main() 