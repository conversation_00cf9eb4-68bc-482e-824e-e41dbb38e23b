import torch

# 添加src目录到Python路径
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import torch.nn as nn
import torch.nn.functional as F
import math


class CrossModalAttention(nn.Module):
    """跨模态注意力机制"""
    
    def __init__(self, in_channels, reduction=16):
        super(CrossModalAttention, self).__init__()
        self.in_channels = in_channels
        self.reduction = reduction
        
        # 用于生成注意力权重的网络
        self.attention_rgb = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(in_channels, in_channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels // reduction, in_channels, 1, bias=False),
            nn.Sigmoid()
        )
        
        self.attention_thermal = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(in_channels, in_channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels // reduction, in_channels, 1, bias=False),
            nn.Sigmoid()
        )
        
        # 跨模态特征交互
        self.cross_modal_conv = nn.Conv2d(in_channels * 2, in_channels, 1, bias=False)
        self.bn = nn.BatchNorm2d(in_channels)
        
    def forward(self, rgb_feat, thermal_feat):
        # 计算各模态的注意力权重
        rgb_attention = self.attention_rgb(rgb_feat)
        thermal_attention = self.attention_thermal(thermal_feat)
        
        # 应用注意力
        rgb_weighted = rgb_feat * rgb_attention
        thermal_weighted = thermal_feat * thermal_attention
        
        # 跨模态特征融合
        cross_modal = torch.cat([rgb_weighted, thermal_weighted], dim=1)
        fused_feat = self.cross_modal_conv(cross_modal)
        fused_feat = self.bn(fused_feat)
        
        return fused_feat


class SpatialAttentionFusion(nn.Module):
    """空间注意力融合模块"""
    
    def __init__(self, in_channels):
        super(SpatialAttentionFusion, self).__init__()
        self.conv1 = nn.Conv2d(2, 1, 7, padding=3, bias=False)
        self.sigmoid = nn.Sigmoid()
        
        # 特征融合卷积
        self.fusion_conv = nn.Conv2d(in_channels * 2, in_channels, 3, padding=1, bias=False)
        self.bn = nn.BatchNorm2d(in_channels)
        self.relu = nn.ReLU(inplace=True)
        
    def forward(self, rgb_feat, thermal_feat):
        # 连接两个模态的特征
        concat_feat = torch.cat([rgb_feat, thermal_feat], dim=1)
        
        # 计算空间注意力
        avg_out = torch.mean(concat_feat, dim=1, keepdim=True)
        max_out, _ = torch.max(concat_feat, dim=1, keepdim=True)
        attention_input = torch.cat([avg_out, max_out], dim=1)
        spatial_attention = self.sigmoid(self.conv1(attention_input))
        
        # 应用空间注意力
        attended_feat = concat_feat * spatial_attention
        
        # 融合特征
        fused_feat = self.fusion_conv(attended_feat)
        fused_feat = self.bn(fused_feat)
        fused_feat = self.relu(fused_feat)
        
        return fused_feat


class FeaturePyramidFusion(nn.Module):
    """特征金字塔融合模块"""
    
    def __init__(self, in_channels_list, out_channels=256):
        super(FeaturePyramidFusion, self).__init__()
        self.in_channels_list = in_channels_list
        self.out_channels = out_channels
        
        # 各层的融合模块
        self.fusion_modules = nn.ModuleList()
        for in_channels in in_channels_list:
            self.fusion_modules.append(
                nn.Sequential(
                    CrossModalAttention(in_channels),
                    nn.Conv2d(in_channels, out_channels, 1, bias=False),
                    nn.BatchNorm2d(out_channels),
                    nn.ReLU(inplace=True)
                )
            )
        
        # 特征金字塔上采样模块
        self.upsample_modules = nn.ModuleList()
        for _ in range(len(in_channels_list) - 1):
            self.upsample_modules.append(
                nn.ConvTranspose2d(out_channels, out_channels, 2, stride=2)
            )
    
    def forward(self, rgb_features, thermal_features):
        """
        Args:
            rgb_features: RGB特征列表，从高分辨率到低分辨率
            thermal_features: 热红外特征列表，从高分辨率到低分辨率
        Returns:
            融合后的特征金字塔
        """
        fused_features = []
        
        # 从最低分辨率开始处理
        for i in range(len(rgb_features)):
            # 融合当前层的RGB和热红外特征
            fused = self.fusion_modules[i](rgb_features[i], thermal_features[i])
            fused_features.append(fused)
        
        # 特征金字塔融合：从上到下传播
        for i in range(len(fused_features) - 2, -1, -1):
            # 上采样低分辨率特征
            upsampled = self.upsample_modules[i](fused_features[i + 1])
            # 与当前层特征相加
            fused_features[i] = fused_features[i] + upsampled
        
        return fused_features


class MultimodalBackbone(nn.Module):
    """多模态主干网络"""
    
    def __init__(self, rgb_backbone, thermal_backbone, fusion_type='cross_attention'):
        super(MultimodalBackbone, self).__init__()
        self.rgb_backbone = rgb_backbone
        self.thermal_backbone = thermal_backbone
        self.fusion_type = fusion_type
        
        # 获取主干网络的输出通道数（假设使用YOLOv8的主干）
        # 这里需要根据实际的主干网络调整
        if hasattr(rgb_backbone, 'out_channels'):
            self.out_channels = rgb_backbone.out_channels
        else:
            # 默认的YOLOv8通道数
            self.out_channels = [256, 512, 1024]
        
        # 创建融合模块
        if fusion_type == 'cross_attention':
            self.fusion_modules = nn.ModuleList([
                CrossModalAttention(ch) for ch in self.out_channels
            ])
        elif fusion_type == 'spatial_attention':
            self.fusion_modules = nn.ModuleList([
                SpatialAttentionFusion(ch) for ch in self.out_channels
            ])
        elif fusion_type == 'pyramid_fusion':
            self.fusion_module = FeaturePyramidFusion(self.out_channels)
        else:
            raise ValueError(f"不支持的融合类型: {fusion_type}")
    
    def forward(self, rgb_input, thermal_input):
        # 提取RGB特征
        rgb_features = self.rgb_backbone(rgb_input)
        
        # 提取热红外特征
        thermal_features = self.thermal_backbone(thermal_input)
        
        # 特征融合
        if self.fusion_type == 'pyramid_fusion':
            fused_features = self.fusion_module(rgb_features, thermal_features)
        else:
            fused_features = []
            for i, (rgb_feat, thermal_feat) in enumerate(zip(rgb_features, thermal_features)):
                fused = self.fusion_modules[i](rgb_feat, thermal_feat)
                fused_features.append(fused)
        
        return fused_features


class ThermalAdaptationModule(nn.Module):
    """热红外图像适应模块，将单通道热红外图像转换为三通道"""
    
    def __init__(self):
        super(ThermalAdaptationModule, self).__init__()
        # 使用1x1卷积将单通道转换为三通道
        self.adapt_conv = nn.Conv2d(1, 3, 1, bias=False)
        self.bn = nn.BatchNorm2d(3)
        self.relu = nn.ReLU(inplace=True)
        
        # 初始化权重，使得输出接近RGB图像的分布
        nn.init.xavier_normal_(self.adapt_conv.weight)
    
    def forward(self, thermal_input):
        """
        Args:
            thermal_input: 形状为 (B, 1, H, W) 的热红外图像
        Returns:
            形状为 (B, 3, H, W) 的适应后图像
        """
        adapted = self.adapt_conv(thermal_input)
        adapted = self.bn(adapted)
        adapted = self.relu(adapted)
        return adapted


class EarlyFusionModule(nn.Module):
    """早期融合模块，在输入层直接融合RGB和热红外图像"""
    
    def __init__(self, fusion_method='concat'):
        super(EarlyFusionModule, self).__init__()
        self.fusion_method = fusion_method
        
        if fusion_method == 'concat':
            # 直接拼接，输出4通道
            self.out_channels = 4
        elif fusion_method == 'weighted_sum':
            # 加权求和，输出3通道
            self.thermal_adapter = ThermalAdaptationModule()
            self.weight_generator = nn.Sequential(
                nn.AdaptiveAvgPool2d(1),
                nn.Conv2d(4, 2, 1),
                nn.Softmax(dim=1)
            )
            self.out_channels = 3
        elif fusion_method == 'attention':
            # 注意力融合
            self.thermal_adapter = ThermalAdaptationModule()
            self.attention = nn.Sequential(
                nn.Conv2d(6, 32, 3, padding=1),
                nn.ReLU(inplace=True),
                nn.Conv2d(32, 1, 1),
                nn.Sigmoid()
            )
            self.out_channels = 3
    
    def forward(self, rgb_input, thermal_input):
        if self.fusion_method == 'concat':
            # 直接拼接RGB和热红外图像
            fused = torch.cat([rgb_input, thermal_input], dim=1)
        
        elif self.fusion_method == 'weighted_sum':
            # 将热红外图像适应为三通道
            thermal_adapted = self.thermal_adapter(thermal_input)
            
            # 计算权重
            concat_input = torch.cat([rgb_input, thermal_input], dim=1)
            weights = self.weight_generator(concat_input)
            
            # 加权融合
            fused = weights[:, 0:1] * rgb_input + weights[:, 1:2] * thermal_adapted
        
        elif self.fusion_method == 'attention':
            # 注意力融合
            thermal_adapted = self.thermal_adapter(thermal_input)
            
            # 生成注意力图
            concat_input = torch.cat([rgb_input, thermal_adapted], dim=1)
            attention_map = self.attention(concat_input)
            
            # 应用注意力
            fused = attention_map * rgb_input + (1 - attention_map) * thermal_adapted
        
        return fused


if __name__ == "__main__":
    # 测试各个融合模块
    batch_size = 4
    channels = 256
    height, width = 64, 64
    
    # 创建测试数据
    rgb_feat = torch.randn(batch_size, channels, height, width)
    thermal_feat = torch.randn(batch_size, channels, height, width)
    
    # 测试跨模态注意力
    cross_attention = CrossModalAttention(channels)
    fused_feat = cross_attention(rgb_feat, thermal_feat)
    print(f"跨模态注意力输出形状: {fused_feat.shape}")
    
    # 测试空间注意力融合
    spatial_fusion = SpatialAttentionFusion(channels)
    fused_feat = spatial_fusion(rgb_feat, thermal_feat)
    print(f"空间注意力融合输出形状: {fused_feat.shape}")
    
    # 测试早期融合
    rgb_input = torch.randn(batch_size, 3, 640, 640)
    thermal_input = torch.randn(batch_size, 1, 640, 640)
    
    early_fusion = EarlyFusionModule('attention')
    fused_input = early_fusion(rgb_input, thermal_input)
    print(f"早期融合输出形状: {fused_input.shape}") 