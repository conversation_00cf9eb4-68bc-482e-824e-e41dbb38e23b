#!/usr/bin/env python3
"""
极简模型专用评估脚本
支持自动检测模型类型并使用正确的架构
"""

import os
import sys
import argparse
import torch
import json
from datetime import datetime

# 添加路径
sys.path.append('.')
sys.path.append('src')

def detect_model_type(model_path):
    """检测模型类型"""
    try:
        checkpoint = torch.load(model_path, map_location='cpu')
        if 'model_state_dict' in checkpoint:
            state_dict = checkpoint['model_state_dict']
        else:
            state_dict = checkpoint
        
        # 检查是否包含极简模型的特征键
        minimal_keys = [
            'shared_backbone.rgb_adapter.weight',
            'shared_backbone.thermal_adapter.weight', 
            'fusion.alpha',
            'detection_head.head.weight'
        ]
        
        minimal_count = sum(1 for key in minimal_keys if key in state_dict)
        
        if minimal_count >= 3:  # 如果包含大部分极简模型特征
            return 'minimal'
        else:
            return 'standard'
    except Exception as e:
        print(f"⚠️ [WARN] 模型类型检测失败: {e}")
        return 'unknown'

def load_minimal_model(model_path, nc=5):
    """加载极简模型"""
    print(f"📦 [MINIMAL] 加载极简多模态模型...")
    
    from src.models.minimal_multimodal import MinimalMultimodalYOLO
    
    # 创建模型
    model = MinimalMultimodalYOLO(nc=nc)
    
    # 加载权重
    checkpoint = torch.load(model_path, map_location='cpu')
    if 'model_state_dict' in checkpoint:
        state_dict = checkpoint['model_state_dict']
    else:
        state_dict = checkpoint
    
    try:
        model.load_state_dict(state_dict)
        print("✅ [SUCCESS] 极简模型权重加载成功")
    except Exception as e:
        print(f"❌ [ERROR] 权重加载失败: {e}")
        print("🔧 [FIX] 尝试部分加载...")
        model.load_state_dict(state_dict, strict=False)
        print("⚠️ [WARN] 使用部分权重加载")
    
    return model

def simple_evaluate(model, data_dir='data', conf_thresh=0.01, device='cuda'):
    """简化的评估函数"""
    print(f"🔍 [EVAL] 开始评估 (置信度阈值: {conf_thresh})...")
    
    model = model.to(device)
    model.eval()
    
    # 检查测试数据
    test_images_dir = os.path.join(data_dir, 'test', 'images')
    test_labels_dir = os.path.join(data_dir, 'test', 'labels')
    
    if not os.path.exists(test_images_dir):
        print(f"❌ [ERROR] 测试图像目录不存在: {test_images_dir}")
        return None
    
    if not os.path.exists(test_labels_dir):
        print(f"❌ [ERROR] 测试标签目录不存在: {test_labels_dir}")
        return None
    
    # 统计测试文件
    import glob
    image_files = glob.glob(os.path.join(test_images_dir, '*.jpg')) + \
                  glob.glob(os.path.join(test_images_dir, '*.png'))
    label_files = glob.glob(os.path.join(test_labels_dir, '*.txt'))
    
    print(f"📊 [DATA] 测试图像: {len(image_files)}, 标签文件: {len(label_files)}")
    
    if len(image_files) == 0:
        print("❌ [ERROR] 没有找到测试图像")
        return None
    
    # 简化的评估结果
    class_names = ['110_two_hight_glass', 'Glass_Dirty', 'Glass_Loss', 'Polyme_Dirty', 'insulator']
    
    # 模拟评估结果（实际应该进行真实推理）
    print("⚠️ [NOTE] 这是简化版评估，仅验证模型加载")
    print("💡 [HINT] 完整评估需要实现数据加载和推理逻辑")
    
    # 创建模拟结果
    mock_results = {
        'mAP': 0.0,
        'AP_per_class': {name: 0.0 for name in class_names},
        'model_type': 'minimal',
        'conf_thresh': conf_thresh,
        'test_images': len(image_files),
        'model_loaded': True
    }
    
    return mock_results

def main():
    parser = argparse.ArgumentParser(description='极简模型评估')
    parser.add_argument('--model_path', type=str, required=True, help='模型路径')
    parser.add_argument('--data_dir', type=str, default='data', help='数据目录')
    parser.add_argument('--conf_thresh', type=float, default=0.01, help='置信度阈值')
    parser.add_argument('--output_dir', type=str, default='runs/eval_minimal', help='输出目录')
    parser.add_argument('--nc', type=int, default=5, help='类别数量')
    
    args = parser.parse_args()
    
    print("🔍 极简模型评估")
    print("=" * 50)
    print(f"📁 模型路径: {args.model_path}")
    print(f"📁 数据目录: {args.data_dir}")
    print(f"🎯 置信度阈值: {args.conf_thresh}")
    print("=" * 50)
    
    # 检查模型文件
    if not os.path.exists(args.model_path):
        print(f"❌ [ERROR] 模型文件不存在: {args.model_path}")
        return
    
    # 检测模型类型
    model_type = detect_model_type(args.model_path)
    print(f"🔍 [DETECT] 模型类型: {model_type}")
    
    if model_type != 'minimal':
        print("⚠️ [WARN] 检测到非极简模型，可能无法正确加载")
        print("💡 [HINT] 请使用标准评估脚本: python evaluate.py")
        
        # 询问是否继续
        response = input("是否继续尝试加载? (y/n): ")
        if response.lower() != 'y':
            return
    
    # 设置设备
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🖥️ [DEVICE] 使用设备: {device}")
    
    try:
        # 加载模型
        model = load_minimal_model(args.model_path, nc=args.nc)
        
        # 评估模型
        results = simple_evaluate(
            model, 
            data_dir=args.data_dir, 
            conf_thresh=args.conf_thresh,
            device=device
        )
        
        if results:
            # 保存结果
            os.makedirs(args.output_dir, exist_ok=True)
            
            results_file = os.path.join(args.output_dir, 'minimal_eval_results.json')
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            print(f"\n📊 [RESULTS] 评估结果:")
            print(f"  模型类型: {results['model_type']}")
            print(f"  置信度阈值: {results['conf_thresh']}")
            print(f"  测试图像数: {results['test_images']}")
            print(f"  模型加载: {'✅' if results['model_loaded'] else '❌'}")
            
            print(f"\n💾 [SAVE] 结果已保存到: {results_file}")
            
            print(f"\n💡 [NEXT] 下一步建议:")
            print(f"  1. 模型加载成功，可以进行完整评估")
            print(f"  2. 实现完整的推理和评估逻辑")
            print(f"  3. 对比不同置信度阈值的效果")
            
        else:
            print("❌ [ERROR] 评估失败")
            
    except Exception as e:
        print(f"❌ [ERROR] 评估过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
