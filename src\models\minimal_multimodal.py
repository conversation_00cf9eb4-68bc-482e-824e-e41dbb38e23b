#!/usr/bin/env python3
"""
极简多模态YOLO模型
专门针对小数据集设计，参数量 < 30万
"""

import torch
import torch.nn as nn
import torch.nn.functional as F

class MinimalConvBlock(nn.Module):
    """极简卷积块"""
    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1):
        super().__init__()
        padding = kernel_size // 2
        self.conv = nn.Conv2d(in_channels, out_channels, kernel_size, stride, padding, bias=False)
        self.bn = nn.BatchNorm2d(out_channels)
        self.act = nn.ReLU(inplace=True)
    
    def forward(self, x):
        return self.act(self.bn(self.conv(x)))

class MinimalBackbone(nn.Module):
    """极简主干网络"""
    
    def __init__(self, in_channels=3):
        super().__init__()
        
        # 极简的特征提取网络，通道数大幅减少
        self.stem = MinimalConvBlock(in_channels, 8, 3, 2)   # /2
        self.stage1 = MinimalConvBlock(8, 16, 3, 2)          # /4
        self.stage2 = MinimalConvBlock(16, 32, 3, 2)         # /8
        self.stage3 = MinimalConvBlock(32, 64, 3, 2)         # /16
        self.stage4 = MinimalConvBlock(64, 128, 3, 2)        # /32
    
    def forward(self, x):
        features = []
        
        x = self.stem(x)      # 8 channels
        x = self.stage1(x)    # 16 channels, /4
        
        x = self.stage2(x)    # 32 channels, /8
        features.append(x)    # P3 level
        
        x = self.stage3(x)    # 64 channels, /16
        features.append(x)    # P4 level
        
        x = self.stage4(x)    # 128 channels, /32
        features.append(x)    # P5 level
        
        return features

class SharedBackbone(nn.Module):
    """共享主干网络，RGB和热红外使用同一个网络"""
    
    def __init__(self):
        super().__init__()
        
        # 输入适配层
        self.rgb_adapter = nn.Conv2d(3, 1, 1, bias=False)  # RGB -> 1通道
        self.thermal_adapter = nn.Conv2d(1, 1, 1, bias=False)  # 热红外保持1通道
        
        # 共享的主干网络
        self.backbone = MinimalBackbone(in_channels=1)
    
    def forward(self, rgb_input, thermal_input):
        # 将RGB转换为单通道
        rgb_adapted = self.rgb_adapter(rgb_input)
        thermal_adapted = self.thermal_adapter(thermal_input)
        
        # 提取特征
        rgb_features = self.backbone(rgb_adapted)
        thermal_features = self.backbone(thermal_adapted)
        
        return rgb_features, thermal_features

class MinimalFusion(nn.Module):
    """极简特征融合"""
    def __init__(self):
        super().__init__()
        # 学习融合权重
        self.alpha = nn.Parameter(torch.tensor(0.5))
    
    def forward(self, rgb_feat, thermal_feat):
        # 简单的加权融合
        return self.alpha * rgb_feat + (1 - self.alpha) * thermal_feat

class MinimalDetectionHead(nn.Module):
    """极简检测头，只使用单尺度检测"""
    
    def __init__(self, in_channels, nc=5):
        super().__init__()
        self.nc = nc
        self.num_outputs = nc + 5  # 类别 + bbox + 置信度
        
        print(f"🔧 [MINIMAL] 检测头配置: nc={nc}, 单尺度检测")
        
        # 只使用最大尺度进行检测，减少计算量
        self.head = nn.Conv2d(in_channels, self.num_outputs, 1)
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化检测头权重"""
        print("🔧 [MINIMAL] 初始化检测头权重...")
        
        nn.init.normal_(self.head.weight, 0, 0.01)
        nn.init.constant_(self.head.bias, 0)
        
        # 设置置信度偏置为负值
        with torch.no_grad():
            self.head.bias[4] = -2.0  # 置信度偏置
        
        print("✅ [MINIMAL] 检测头初始化完成")
    
    def forward(self, features):
        # 只使用最大的特征图（最后一个）
        feature = features[-1]  # 使用最小分辨率的特征图
        output = self.head(feature)
        return [output]  # 返回列表保持接口一致

class MinimalMultimodalYOLO(nn.Module):
    """极简多模态YOLO模型
    
    设计目标：
    - 参数量 < 30万
    - 参数/样本比例 < 500
    - 共享主干网络减少参数
    - 单尺度检测减少计算
    """
    
    def __init__(self, nc=5):
        super().__init__()
        self.nc = nc
        
        print(f"🚀 [MINIMAL] 创建极简多模态模型: nc={nc}")
        
        # 共享主干网络
        self.shared_backbone = SharedBackbone()
        
        # 特征通道数（只使用最后一层）
        self.feature_channels = 128
        
        # 极简融合模块
        self.fusion = MinimalFusion()
        
        # 极简检测头（单尺度）
        self.detection_head = MinimalDetectionHead(self.feature_channels, nc)
        
        # 计算并打印参数量
        self._print_model_info()
    
    def _print_model_info(self):
        """打印模型信息"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        print(f"📊 [MINIMAL] 模型参数统计:")
        print(f"  总参数量: {total_params:,}")
        print(f"  可训练参数: {trainable_params:,}")
        print(f"  模型大小: {total_params * 4 / 1024 / 1024:.2f} MB")
        
        # 与训练样本的比例
        train_samples = 553
        ratio = total_params / train_samples
        print(f"  参数/样本比例: {ratio:.1f} (目标: <500)")
        
        if ratio < 100:
            print("  ✅ 参数量非常合理，适合极小数据集")
        elif ratio < 500:
            print("  ✅ 参数量合理，适合小数据集")
        else:
            print("  ⚠️ 参数量仍偏高")
        
        # 各模块参数量
        print(f"\n📊 [MINIMAL] 各模块参数量:")
        for name, module in self.named_children():
            module_params = sum(p.numel() for p in module.parameters())
            print(f"  {name}: {module_params:,} ({module_params/total_params*100:.1f}%)")
    
    def forward(self, rgb_input, thermal_input):
        """前向传播"""
        # 提取特征（共享主干网络）
        rgb_features, thermal_features = self.shared_backbone(rgb_input, thermal_input)
        
        # 特征融合（只融合最后一层）
        fused_feature = self.fusion(rgb_features[-1], thermal_features[-1])
        
        # 检测头输出（单尺度）
        outputs = self.detection_head([fused_feature])
        
        return outputs

if __name__ == '__main__':
    # 添加路径
    import sys
    sys.path.append('.')
    sys.path.append('../..')
    
    # 测试极简模型
    print("🧪 测试极简多模态模型...")
    
    model = MinimalMultimodalYOLO(nc=5)
    
    # 创建测试输入
    rgb_input = torch.randn(1, 3, 640, 640)
    thermal_input = torch.randn(1, 1, 640, 640)
    
    # 前向传播
    with torch.no_grad():
        outputs = model(rgb_input, thermal_input)
    
    print(f"\n📤 输出信息:")
    for i, output in enumerate(outputs):
        print(f"  尺度{i}: {output.shape}")
    
    print("\n✅ 极简模型测试完成！")
    print("\n💡 优势:")
    print("  1. 参数量大幅减少，避免过拟合")
    print("  2. 共享主干网络，提高参数效率")
    print("  3. 单尺度检测，减少计算复杂度")
    print("  4. 适合小数据集训练")
    
    print("\n🔧 使用建议:")
    print("  1. 增加训练轮数（200-300轮）")
    print("  2. 使用强数据增强")
    print("  3. 降低学习率，精细调优")
    print("  4. 重点关注少数类别的权重设置")
