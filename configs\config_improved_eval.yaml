# 改进的评估配置文件
# 针对绝缘子串多模态检测的优化配置

# 数据配置
data_dir: data
batch_size: 16
img_size: 640
num_workers: 0  # Windows系统建议设为0

# 模型配置
model:
  type: simple
  fusion_type: cross_attention
  nc: 5

# 评估参数优化
evaluation:
  conf_thresh: 0.25      # 提高置信度阈值，减少误检
  iou_thresh: 0.5        # 调整IoU阈值
  nms_thresh: 0.5        # NMS阈值
  max_detections: 100    # 每张图像最大检测数
  
# 类别权重配置（根据样本分布调整）
class_weights:
  - 1.0    # 110_two_hight_glass (180 samples) - 基准
  - 1.1    # Glass_Dirty (158 samples) - 略微增加
  - 2.5    # Glass_Loss (38 samples) - 大幅增加
  - 1.2    # Polyme_Dirty (148 samples) - 略微增加  
  - 3.0    # insulator (29 samples) - 最大增加

# 类别名称映射
class_names:
  - "110_two_hight_glass"
  - "Glass_Dirty" 
  - "Glass_Loss"
  - "Polyme_Dirty"
  - "insulator"

# 后处理优化
postprocess:
  coordinate_precision: 6    # 坐标精度位数
  bbox_clamp: true          # 边界框坐标限制在[0,1]
  filter_invalid_boxes: true # 过滤无效边界框
  class_specific_nms: true   # 分类别进行NMS

# 调试选项
debug:
  save_predictions: true     # 保存预测结果
  verbose_output: true       # 详细输出
  plot_results: true         # 绘制结果图表
