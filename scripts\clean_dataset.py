#!/usr/bin/env python3
"""
数据集清理脚本
1. 删除多目标检测样本（一张图多个对象）
2. 统一标签格式为标准YOLO格式
3. 清理不符合要求的样本

目标：创建一个简洁、一致的单目标检测数据集
"""

import os
import shutil
from pathlib import Path
import argparse
from typing import List, Tuple, Dict

class DatasetCleaner:
    """数据集清理器"""
    
    def __init__(self, data_dir: str, backup_dir: str = None):
        self.data_dir = Path(data_dir)
        self.backup_dir = Path(backup_dir) if backup_dir else self.data_dir.parent / "data_backup"
        
        self.stats = {
            'total_samples': 0,
            'multi_target_removed': 0,
            'invalid_format_removed': 0,
            'bbox_issues_fixed': 0,
            'clean_samples_kept': 0,
            'removed_files': []
        }
    
    def analyze_dataset(self, splits: List[str] = ['train', 'valid', 'test']) -> Dict:
        """分析数据集质量"""
        print("🔍 分析数据集质量...")
        
        analysis = {}
        
        for split in splits:
            print(f"\n📊 分析 {split} 数据集:")
            
            images_dir = self.data_dir / split / 'images'
            labels_dir = self.data_dir / split / 'labels'
            thermal_dir = self.data_dir / split / 'images' / 'thermal'
            
            if not all([images_dir.exists(), labels_dir.exists()]):
                print(f"⚠️ {split} 目录结构不完整")
                continue
            
            # 获取所有图像和标签文件
            image_files = list(images_dir.glob('*.jpg')) + list(images_dir.glob('*.png'))
            label_files = list(labels_dir.glob('*.txt'))
            
            # 分析统计
            split_stats = {
                'total_images': len(image_files),
                'total_labels': len(label_files),
                'multi_target_labels': 0,
                'single_target_labels': 0,
                'empty_labels': 0,
                'invalid_labels': 0,
                'missing_pairs': 0,
                'thermal_missing': 0,
                'multi_target_files': [],
                'invalid_files': [],
                'missing_thermal_files': []
            }
            
            # 检查每个标签文件
            for label_file in label_files:
                try:
                    with open(label_file, 'r', encoding='utf-8') as f:
                        lines = [line.strip() for line in f.readlines() if line.strip()]
                    
                    if not lines:
                        split_stats['empty_labels'] += 1
                        split_stats['invalid_files'].append(label_file.name)
                        continue
                    
                    # 检查标签格式和目标数量
                    valid_lines = 0
                    for line in lines:
                        parts = line.split()
                        if len(parts) == 5:  # 标准YOLO格式
                            try:
                                class_id = int(parts[0])
                                coords = [float(x) for x in parts[1:5]]
                                # 检查坐标范围
                                if all(0 <= coord <= 1 for coord in coords):
                                    valid_lines += 1
                            except ValueError:
                                pass
                        elif len(parts) > 5:
                            # 可能是多边形格式，标记为无效
                            split_stats['invalid_files'].append(label_file.name)
                            break
                    
                    if valid_lines == len(lines):
                        if len(lines) == 1:
                            split_stats['single_target_labels'] += 1
                        else:
                            split_stats['multi_target_labels'] += 1
                            split_stats['multi_target_files'].append(label_file.name)
                    else:
                        split_stats['invalid_labels'] += 1
                        split_stats['invalid_files'].append(label_file.name)
                
                except Exception as e:
                    split_stats['invalid_labels'] += 1
                    split_stats['invalid_files'].append(label_file.name)
            
            # 检查图像-标签对应关系
            image_stems = {f.stem for f in image_files}
            label_stems = {f.stem for f in label_files}
            
            split_stats['missing_pairs'] = len(image_stems.symmetric_difference(label_stems))
            
            # 检查热红外图像
            if thermal_dir.exists():
                for image_file in image_files:
                    thermal_name = f"{image_file.stem}_thermal.png"
                    thermal_path = thermal_dir / thermal_name
                    if not thermal_path.exists():
                        split_stats['thermal_missing'] += 1
                        split_stats['missing_thermal_files'].append(image_file.name)
            
            analysis[split] = split_stats
            
            # 打印统计结果
            print(f"  总图像数: {split_stats['total_images']}")
            print(f"  总标签数: {split_stats['total_labels']}")
            print(f"  单目标标签: {split_stats['single_target_labels']}")
            print(f"  多目标标签: {split_stats['multi_target_labels']}")
            print(f"  空标签: {split_stats['empty_labels']}")
            print(f"  无效标签: {split_stats['invalid_labels']}")
            print(f"  缺少配对: {split_stats['missing_pairs']}")
            print(f"  缺少热红外: {split_stats['thermal_missing']}")
        
        return analysis
    
    def backup_dataset(self, splits: List[str] = ['train', 'valid', 'test']):
        """备份原始数据集"""
        print("📦 备份原始数据集...")
        
        if self.backup_dir.exists():
            print(f"⚠️ 备份目录已存在: {self.backup_dir}")
            return
        
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        for split in splits:
            split_dir = self.data_dir / split
            if split_dir.exists():
                backup_split_dir = self.backup_dir / split
                shutil.copytree(split_dir, backup_split_dir)
                print(f"✅ 已备份 {split} 数据集")
    
    def clean_dataset(self, splits: List[str] = ['train', 'valid', 'test'], 
                     remove_multi_target: bool = True,
                     remove_invalid_format: bool = True):
        """清理数据集"""
        print("🧹 开始清理数据集...")
        
        for split in splits:
            print(f"\n🔄 清理 {split} 数据集...")
            
            images_dir = self.data_dir / split / 'images'
            labels_dir = self.data_dir / split / 'labels'
            thermal_dir = self.data_dir / split / 'images' / 'thermal'
            
            if not all([images_dir.exists(), labels_dir.exists()]):
                print(f"⚠️ {split} 目录不存在，跳过")
                continue
            
            label_files = list(labels_dir.glob('*.txt'))
            removed_count = 0
            
            for label_file in label_files:
                should_remove = False
                remove_reason = ""
                
                try:
                    with open(label_file, 'r', encoding='utf-8') as f:
                        lines = [line.strip() for line in f.readlines() if line.strip()]
                    
                    # 检查是否为空文件
                    if not lines:
                        should_remove = True
                        remove_reason = "空标签文件"
                    
                    # 检查多目标
                    elif remove_multi_target and len(lines) > 1:
                        should_remove = True
                        remove_reason = f"多目标检测({len(lines)}个目标)"
                        self.stats['multi_target_removed'] += 1
                    
                    # 检查格式有效性
                    elif remove_invalid_format:
                        valid_format = True
                        for line in lines:
                            parts = line.split()
                            if len(parts) != 5:  # 非标准YOLO格式
                                valid_format = False
                                break
                            try:
                                class_id = int(parts[0])
                                coords = [float(x) for x in parts[1:5]]
                                # 检查坐标范围
                                if not all(0 <= coord <= 1 for coord in coords):
                                    valid_format = False
                                    break
                            except ValueError:
                                valid_format = False
                                break
                        
                        if not valid_format:
                            should_remove = True
                            remove_reason = "标签格式无效"
                            self.stats['invalid_format_removed'] += 1
                    
                    # 执行删除
                    if should_remove:
                        # 删除标签文件
                        label_file.unlink()
                        
                        # 删除对应的RGB图像
                        rgb_extensions = ['.jpg', '.jpeg', '.png']
                        for ext in rgb_extensions:
                            rgb_path = images_dir / f"{label_file.stem}{ext}"
                            if rgb_path.exists():
                                rgb_path.unlink()
                                break
                        
                        # 删除对应的热红外图像
                        if thermal_dir.exists():
                            thermal_path = thermal_dir / f"{label_file.stem}_thermal.png"
                            if thermal_path.exists():
                                thermal_path.unlink()
                        
                        removed_count += 1
                        self.stats['removed_files'].append({
                            'split': split,
                            'file': label_file.name,
                            'reason': remove_reason
                        })
                        
                        print(f"  ❌ 删除: {label_file.name} ({remove_reason})")
                    else:
                        self.stats['clean_samples_kept'] += 1
                
                except Exception as e:
                    print(f"  ⚠️ 处理 {label_file.name} 时出错: {e}")
            
            print(f"✅ {split} 数据集清理完成，删除了 {removed_count} 个样本")
    
    def validate_cleaned_dataset(self, splits: List[str] = ['train', 'valid', 'test']):
        """验证清理后的数据集"""
        print("\n✅ 验证清理后的数据集...")
        
        total_samples = 0
        all_single_target = True
        all_valid_format = True
        
        for split in splits:
            labels_dir = self.data_dir / split / 'labels'
            if not labels_dir.exists():
                continue
            
            label_files = list(labels_dir.glob('*.txt'))
            split_samples = len(label_files)
            total_samples += split_samples
            
            print(f"  {split}: {split_samples} 个样本")
            
            # 检查每个标签文件
            for label_file in label_files:
                try:
                    with open(label_file, 'r', encoding='utf-8') as f:
                        lines = [line.strip() for line in f.readlines() if line.strip()]
                    
                    # 检查是否单目标
                    if len(lines) != 1:
                        all_single_target = False
                        print(f"    ⚠️ {label_file.name} 不是单目标")
                    
                    # 检查格式
                    for line in lines:
                        parts = line.split()
                        if len(parts) != 5:
                            all_valid_format = False
                            print(f"    ⚠️ {label_file.name} 格式不标准")
                            break
                        
                        try:
                            class_id = int(parts[0])
                            coords = [float(x) for x in parts[1:5]]
                            if not all(0 <= coord <= 1 for coord in coords):
                                all_valid_format = False
                                print(f"    ⚠️ {label_file.name} 坐标超出范围")
                                break
                        except ValueError:
                            all_valid_format = False
                            print(f"    ⚠️ {label_file.name} 坐标格式错误")
                            break
                
                except Exception as e:
                    all_valid_format = False
                    print(f"    ❌ {label_file.name} 读取失败: {e}")
        
        print(f"\n📊 清理后统计:")
        print(f"  总样本数: {total_samples}")
        print(f"  全部单目标: {'✅' if all_single_target else '❌'}")
        print(f"  格式标准: {'✅' if all_valid_format else '❌'}")
        
        return total_samples, all_single_target, all_valid_format
    
    def generate_report(self):
        """生成清理报告"""
        report = f"""
数据集清理报告
===============
总样本数: {self.stats['total_samples']}
删除多目标样本: {self.stats['multi_target_removed']}
删除无效格式样本: {self.stats['invalid_format_removed']}
保留干净样本: {self.stats['clean_samples_kept']}

删除的文件详情:
"""
        for removed in self.stats['removed_files']:
            report += f"- {removed['split']}/{removed['file']}: {removed['reason']}\n"
        
        # 保存报告
        report_path = self.data_dir.parent / "dataset_cleaning_report.txt"
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"📋 清理报告已保存到: {report_path}")
        return report

def main():
    parser = argparse.ArgumentParser(description='数据集清理工具')
    parser.add_argument('--data_dir', type=str, default='data', help='数据集目录')
    parser.add_argument('--backup_dir', type=str, help='备份目录')
    parser.add_argument('--splits', nargs='+', default=['train', 'valid', 'test'], 
                       help='要处理的数据分割')
    parser.add_argument('--analyze_only', action='store_true', help='仅分析不清理')
    parser.add_argument('--keep_multi_target', action='store_true', 
                       help='保留多目标样本')
    parser.add_argument('--no_backup', action='store_true', help='跳过备份')
    
    args = parser.parse_args()
    
    print("🧹 数据集清理工具")
    print("=" * 50)
    print("目标：创建简洁一致的单目标检测数据集")
    print("策略：删除多目标样本，统一YOLO格式")
    print("=" * 50)
    
    # 创建清理器
    cleaner = DatasetCleaner(args.data_dir, args.backup_dir)
    
    # 分析数据集
    analysis = cleaner.analyze_dataset(args.splits)
    
    if args.analyze_only:
        print("\n📊 仅执行分析，跳过清理步骤")
        return
    
    # 备份数据集
    if not args.no_backup:
        cleaner.backup_dataset(args.splits)
    else:
        print("⚠️ 跳过备份步骤")
    
    # 清理数据集
    cleaner.clean_dataset(
        splits=args.splits,
        remove_multi_target=(not args.keep_multi_target),
        remove_invalid_format=True
    )
    
    # 验证清理结果
    total_samples, all_single, all_valid = cleaner.validate_cleaned_dataset(args.splits)
    
    # 生成报告
    report = cleaner.generate_report()
    
    print("\n" + "=" * 50)
    print("🎉 数据集清理完成！")
    
    if all_single and all_valid:
        print("✅ 数据集现在是干净的单目标检测数据集")
        print("建议使用清理后的数据集重新训练模型")
    else:
        print("⚠️ 数据集可能仍有问题，请检查验证结果")

if __name__ == '__main__':
    main()