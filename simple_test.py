#!/usr/bin/env python3
"""
简单的模型测试脚本
"""

import os
import sys
import torch

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.join(current_dir, 'src'))
sys.path.insert(0, current_dir)

def test_model_loading():
    """测试模型加载"""
    print("🔍 测试模型加载...")
    
    # 尝试导入模型
    try:
        from src.models.multimodal_yolo_fixed import FixedMultimodalYOLO as SimpleMultimodalYOLO
        print("✅ 模型类导入成功")
    except ImportError as e:
        print(f"❌ 模型类导入失败: {e}")
        return False
    
    # 尝试创建模型
    try:
        model = SimpleMultimodalYOLO(nc=5, fusion_type='cross_attention')
        print("✅ 模型创建成功")
    except Exception as e:
        print(f"❌ 模型创建失败: {e}")
        return False
    
    # 尝试加载权重
    model_path = "runs/train_progressive/20250802_170243/stage3/weights/best.pt"
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return False
    
    try:
        checkpoint = torch.load(model_path, map_location='cpu')
        print(f"✅ 权重文件加载成功")
        print(f"📋 权重文件包含: {list(checkpoint.keys())}")
        
        if 'model_state_dict' in checkpoint:
            model_weights = checkpoint['model_state_dict']
            missing_keys, unexpected_keys = model.load_state_dict(model_weights, strict=False)
            print(f"✅ 模型权重加载成功")
            print(f"⚠️ 缺失键数量: {len(missing_keys)}")
            print(f"⚠️ 意外键数量: {len(unexpected_keys)}")
        else:
            print("❌ 未找到model_state_dict")
            return False
            
    except Exception as e:
        print(f"❌ 权重加载失败: {e}")
        return False
    
    return True

def test_simple_inference():
    """测试简单推理"""
    print("\n🔍 测试简单推理...")
    
    try:
        from src.models.multimodal_yolo_fixed import FixedMultimodalYOLO as SimpleMultimodalYOLO
        
        # 创建模型
        model = SimpleMultimodalYOLO(nc=5, fusion_type='cross_attention')
        
        # 加载权重
        model_path = "runs/train_progressive/20250802_170243/stage3/weights/best.pt"
        checkpoint = torch.load(model_path, map_location='cpu')
        model.load_state_dict(checkpoint['model_state_dict'], strict=False)
        model.eval()
        
        # 创建虚拟输入
        rgb_input = torch.randn(1, 3, 640, 640)
        thermal_input = torch.randn(1, 1, 640, 640)
        
        print("✅ 输入数据创建成功")
        print(f"  RGB输入形状: {rgb_input.shape}")
        print(f"  热红外输入形状: {thermal_input.shape}")
        
        # 推理
        with torch.no_grad():
            outputs = model(rgb_input, thermal_input)
        
        print("✅ 模型推理成功")
        print(f"📊 输出分析:")
        print(f"  输出数量: {len(outputs)}")
        
        for i, output in enumerate(outputs):
            print(f"  输出{i}: 形状={output.shape}")
            if len(output.shape) == 4:
                b, c, h, w = output.shape
                if c >= 5:
                    # 分析置信度
                    conf_raw = output[0, 4, :, :]
                    conf_sigmoid = torch.sigmoid(conf_raw)
                    print(f"    置信度原始范围: [{conf_raw.min():.4f}, {conf_raw.max():.4f}]")
                    print(f"    置信度Sigmoid范围: [{conf_sigmoid.min():.6f}, {conf_sigmoid.max():.6f}]")
                    print(f"    置信度均值: {conf_sigmoid.mean():.6f}")
                    
                    # 统计激活
                    total = h * w
                    for thresh in [0.01, 0.001, 0.0001]:
                        count = (conf_sigmoid > thresh).sum().item()
                        print(f"    置信度 > {thresh}: {count}/{total}")
        
        return True
        
    except Exception as e:
        print(f"❌ 推理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🚀 简单模型测试开始...")
    print("-" * 50)
    
    # 测试模型加载
    if not test_model_loading():
        print("❌ 模型加载测试失败")
        return
    
    # 测试推理
    if not test_simple_inference():
        print("❌ 推理测试失败")
        return
    
    print("\n🎯 测试结论:")
    print("✅ 模型可以正常加载和推理")
    print("💡 建议使用极低置信度阈值进行评估:")
    print("   python evaluate.py --model_path runs/train_progressive/20250802_170243/stage3/weights/best.pt --data_dir data --conf_thresh 0.0001")

if __name__ == '__main__':
    main()
