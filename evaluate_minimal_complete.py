#!/usr/bin/env python3
"""
极简模型完整评估脚本
实现真正的推理和mAP计算
"""

import os
import sys
import argparse
import torch
import json
import glob
from datetime import datetime
from collections import defaultdict
import numpy as np

# 添加路径
sys.path.append('.')
sys.path.append('src')

def load_minimal_model(model_path, nc=5, device='cuda'):
    """加载极简模型"""
    print(f"📦 [MINIMAL] 加载极简多模态模型...")
    
    from src.models.minimal_multimodal import MinimalMultimodalYOLO
    
    # 创建模型
    model = MinimalMultimodalYOLO(nc=nc)
    
    # 加载权重
    checkpoint = torch.load(model_path, map_location='cpu')
    if 'model_state_dict' in checkpoint:
        state_dict = checkpoint['model_state_dict']
    else:
        state_dict = checkpoint
    
    model.load_state_dict(state_dict)
    model = model.to(device)
    model.eval()
    
    print("✅ [SUCCESS] 极简模型加载成功")
    return model

def load_image_simple(image_path, img_size=640):
    """简化的图像加载函数"""
    try:
        import cv2
        
        # 加载RGB图像
        rgb_img = cv2.imread(image_path)
        if rgb_img is None:
            return None, None
        
        rgb_img = cv2.cvtColor(rgb_img, cv2.COLOR_BGR2RGB)
        
        # 查找对应的热红外图像
        thermal_path = image_path.replace('images', 'thermal').replace('.jpg', '.png')
        if not os.path.exists(thermal_path):
            thermal_path = image_path.replace('images', 'thermal')
        
        if os.path.exists(thermal_path):
            thermal_img = cv2.imread(thermal_path, cv2.IMREAD_GRAYSCALE)
        else:
            # 如果没有热红外图像，创建一个零图像
            thermal_img = np.zeros((rgb_img.shape[0], rgb_img.shape[1]), dtype=np.uint8)
        
        # 调整大小
        rgb_img = cv2.resize(rgb_img, (img_size, img_size))
        thermal_img = cv2.resize(thermal_img, (img_size, img_size))
        
        # 转换为tensor
        rgb_tensor = torch.from_numpy(rgb_img).permute(2, 0, 1).float() / 255.0
        thermal_tensor = torch.from_numpy(thermal_img).unsqueeze(0).float() / 255.0
        
        # 添加batch维度
        rgb_tensor = rgb_tensor.unsqueeze(0)
        thermal_tensor = thermal_tensor.unsqueeze(0)
        
        return rgb_tensor, thermal_tensor
        
    except ImportError:
        print("❌ [ERROR] OpenCV未安装，无法加载图像")
        return None, None
    except Exception as e:
        print(f"❌ [ERROR] 图像加载失败: {e}")
        return None, None

def load_labels(label_path):
    """加载YOLO格式标签"""
    if not os.path.exists(label_path):
        return []
    
    labels = []
    try:
        with open(label_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line:
                    parts = line.split()
                    if len(parts) >= 5:
                        class_id = int(parts[0])
                        x_center = float(parts[1])
                        y_center = float(parts[2])
                        width = float(parts[3])
                        height = float(parts[4])
                        labels.append([class_id, x_center, y_center, width, height])
    except Exception as e:
        print(f"⚠️ [WARN] 标签加载失败 {label_path}: {e}")
    
    return labels

def calculate_iou(box1, box2):
    """计算两个边界框的IoU"""
    # box格式: [x_center, y_center, width, height]
    
    # 转换为 [x1, y1, x2, y2] 格式
    def xywh_to_xyxy(box):
        x_center, y_center, w, h = box
        x1 = x_center - w / 2
        y1 = y_center - h / 2
        x2 = x_center + w / 2
        y2 = y_center + h / 2
        return [x1, y1, x2, y2]
    
    box1_xyxy = xywh_to_xyxy(box1)
    box2_xyxy = xywh_to_xyxy(box2)
    
    # 计算交集
    x1 = max(box1_xyxy[0], box2_xyxy[0])
    y1 = max(box1_xyxy[1], box2_xyxy[1])
    x2 = min(box1_xyxy[2], box2_xyxy[2])
    y2 = min(box1_xyxy[3], box2_xyxy[3])
    
    if x2 <= x1 or y2 <= y1:
        return 0.0
    
    intersection = (x2 - x1) * (y2 - y1)
    
    # 计算并集
    area1 = (box1_xyxy[2] - box1_xyxy[0]) * (box1_xyxy[3] - box1_xyxy[1])
    area2 = (box2_xyxy[2] - box2_xyxy[0]) * (box2_xyxy[3] - box2_xyxy[1])
    union = area1 + area2 - intersection
    
    return intersection / union if union > 0 else 0.0

def parse_model_output(outputs, conf_thresh=0.01, img_size=640):
    """解析模型输出"""
    if not outputs or len(outputs) == 0:
        return []
    
    # 极简模型只有一个输出
    output = outputs[0]  # [batch, anchors * (nc + 5), height, width]
    
    batch_size, channels, height, width = output.shape
    nc = 5  # 类别数
    num_outputs = nc + 5  # 类别 + bbox + 置信度
    
    # 重塑输出
    output = output.view(batch_size, 1, num_outputs, height, width)
    output = output.permute(0, 1, 3, 4, 2).contiguous()  # [batch, anchors, height, width, outputs]
    
    detections = []
    
    for b in range(batch_size):
        for h in range(height):
            for w in range(width):
                for a in range(1):  # 只有1个anchor
                    pred = output[b, a, h, w]
                    
                    # 解析预测
                    x_center = (pred[0].sigmoid() + w) / width
                    y_center = (pred[1].sigmoid() + h) / height
                    width_pred = pred[2].exp() / width
                    height_pred = pred[3].exp() / height
                    
                    obj_conf = pred[4].sigmoid()
                    
                    if obj_conf > conf_thresh:
                        class_probs = pred[5:].softmax(dim=0)
                        class_conf, class_id = class_probs.max(dim=0)
                        
                        final_conf = obj_conf * class_conf
                        
                        if final_conf > conf_thresh:
                            detections.append({
                                'class_id': class_id.item(),
                                'confidence': final_conf.item(),
                                'bbox': [x_center.item(), y_center.item(), width_pred.item(), height_pred.item()]
                            })
    
    return detections

def evaluate_minimal_model(model, data_dir='data', conf_thresh=0.01, iou_thresh=0.5, device='cuda'):
    """完整评估极简模型"""
    print(f"🔍 [EVAL] 开始完整评估...")
    
    # 获取测试文件
    test_images_dir = os.path.join(data_dir, 'test', 'images')
    test_labels_dir = os.path.join(data_dir, 'test', 'labels')
    
    image_files = glob.glob(os.path.join(test_images_dir, '*.jpg')) + \
                  glob.glob(os.path.join(test_images_dir, '*.png'))
    
    print(f"📊 [DATA] 找到 {len(image_files)} 个测试图像")
    
    class_names = ['110_two_hight_glass', 'Glass_Dirty', 'Glass_Loss', 'Polyme_Dirty', 'insulator']
    
    # 统计真实标签和预测结果
    all_gt_boxes = defaultdict(list)  # 真实框
    all_pred_boxes = defaultdict(list)  # 预测框
    
    total_gt = defaultdict(int)  # 每个类别的真实目标数量
    
    for i, image_path in enumerate(image_files):
        if i % 10 == 0:
            print(f"📊 [PROGRESS] 处理 {i+1}/{len(image_files)} 图像...")
        
        # 加载图像
        rgb_tensor, thermal_tensor = load_image_simple(image_path)
        if rgb_tensor is None:
            continue
        
        rgb_tensor = rgb_tensor.to(device)
        thermal_tensor = thermal_tensor.to(device)
        
        # 加载标签
        label_path = image_path.replace('images', 'labels').replace('.jpg', '.txt').replace('.png', '.txt')
        gt_labels = load_labels(label_path)
        
        # 统计真实标签
        for label in gt_labels:
            class_id = label[0]
            total_gt[class_id] += 1
            all_gt_boxes[class_id].append({
                'bbox': label[1:5],
                'image_id': i,
                'matched': False
            })
        
        # 模型推理
        with torch.no_grad():
            try:
                outputs = model(rgb_tensor, thermal_tensor)
                detections = parse_model_output(outputs, conf_thresh)
                
                # 保存预测结果
                for det in detections:
                    class_id = det['class_id']
                    all_pred_boxes[class_id].append({
                        'bbox': det['bbox'],
                        'confidence': det['confidence'],
                        'image_id': i
                    })
                    
            except Exception as e:
                print(f"⚠️ [WARN] 推理失败 {image_path}: {e}")
                continue
    
    # 计算AP
    print(f"📊 [CALC] 计算各类别AP...")
    
    ap_per_class = {}
    
    for class_id in range(len(class_names)):
        class_name = class_names[class_id]
        
        gt_boxes = all_gt_boxes[class_id]
        pred_boxes = all_pred_boxes[class_id]
        
        if len(gt_boxes) == 0:
            ap_per_class[class_name] = 0.0
            print(f"  {class_name}: 无真实目标")
            continue
        
        if len(pred_boxes) == 0:
            ap_per_class[class_name] = 0.0
            print(f"  {class_name}: 无预测结果")
            continue
        
        # 按置信度排序
        pred_boxes.sort(key=lambda x: x['confidence'], reverse=True)
        
        # 计算精度和召回率
        tp = 0
        fp = 0
        
        for pred in pred_boxes:
            best_iou = 0
            best_gt_idx = -1
            
            # 找到最佳匹配的真实框
            for gt_idx, gt in enumerate(gt_boxes):
                if gt['image_id'] == pred['image_id'] and not gt['matched']:
                    iou = calculate_iou(pred['bbox'], gt['bbox'])
                    if iou > best_iou:
                        best_iou = iou
                        best_gt_idx = gt_idx
            
            if best_iou >= iou_thresh:
                tp += 1
                gt_boxes[best_gt_idx]['matched'] = True
            else:
                fp += 1
        
        # 计算AP (简化版本)
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0
        recall = tp / len(gt_boxes) if len(gt_boxes) > 0 else 0
        
        # 简化的AP计算 (实际应该计算PR曲线下面积)
        ap = precision * recall
        ap_per_class[class_name] = ap
        
        print(f"  {class_name}: TP={tp}, FP={fp}, GT={len(gt_boxes)}, P={precision:.3f}, R={recall:.3f}, AP={ap:.3f}")
    
    # 计算mAP
    mAP = sum(ap_per_class.values()) / len(ap_per_class)
    
    results = {
        'mAP': mAP,
        'AP_per_class': ap_per_class,
        'total_gt': dict(total_gt),
        'conf_thresh': conf_thresh,
        'iou_thresh': iou_thresh,
        'test_images': len(image_files)
    }
    
    return results

def main():
    parser = argparse.ArgumentParser(description='极简模型完整评估')
    parser.add_argument('--model_path', type=str, required=True, help='模型路径')
    parser.add_argument('--data_dir', type=str, default='data', help='数据目录')
    parser.add_argument('--conf_thresh', type=float, default=0.01, help='置信度阈值')
    parser.add_argument('--iou_thresh', type=float, default=0.5, help='IoU阈值')
    parser.add_argument('--output_dir', type=str, default='runs/eval_minimal_complete', help='输出目录')
    
    args = parser.parse_args()
    
    print("🔍 极简模型完整评估")
    print("=" * 50)
    print(f"📁 模型路径: {args.model_path}")
    print(f"📁 数据目录: {args.data_dir}")
    print(f"🎯 置信度阈值: {args.conf_thresh}")
    print(f"🎯 IoU阈值: {args.iou_thresh}")
    print("=" * 50)
    
    # 设置设备
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🖥️ [DEVICE] 使用设备: {device}")
    
    try:
        # 加载模型
        model = load_minimal_model(args.model_path, device=device)
        
        # 完整评估
        results = evaluate_minimal_model(
            model,
            data_dir=args.data_dir,
            conf_thresh=args.conf_thresh,
            iou_thresh=args.iou_thresh,
            device=device
        )
        
        # 显示结果
        print(f"\n📊 [RESULTS] 评估结果:")
        print(f"  mAP: {results['mAP']:.4f}")
        print(f"  各类别AP:")
        for class_name, ap in results['AP_per_class'].items():
            print(f"    {class_name}: {ap:.4f}")
        
        # 保存结果
        os.makedirs(args.output_dir, exist_ok=True)
        results_file = os.path.join(args.output_dir, 'complete_eval_results.json')
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 [SAVE] 结果已保存到: {results_file}")
        
    except Exception as e:
        print(f"❌ [ERROR] 评估失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
