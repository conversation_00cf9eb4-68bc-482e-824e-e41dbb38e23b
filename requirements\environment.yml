# Conda环境配置文件 - 绝缘子串多模态检测项目
# 使用方法：
#   创建环境: conda env create -f environment.yml
#   激活环境: conda activate multimodal-detection
#   更新环境: conda env update -f environment.yml --prune

name: multimodal-detection

channels:
  - pytorch
  - nvidia
  - conda-forge
  - defaults

dependencies:
  # Python版本
  - python=3.9

  # 深度学习框架 (GPU版本，支持CUDA 11.8/12.1)
  - pytorch>=2.0.0
  - torchvision>=0.15.0
  - torchaudio>=2.0.0
  - pytorch-cuda=11.8  # 或者 pytorch-cuda=12.1，根据您的CUDA版本选择

  # 数据处理核心库
  - numpy>=1.24.0
  - pandas>=2.0.0

  # 图像处理
  - opencv>=4.8.0
  - pillow>=9.5.0

  # 可视化
  - matplotlib>=3.7.0
  - seaborn>=0.12.0

  # 机器学习工具
  - scikit-learn>=1.3.0

  # 进度条
  - tqdm>=4.65.0

  # 配置文件处理
  - pyyaml>=6.0

  # TensorBoard支持
  - tensorboard>=2.13.0

  # 通过pip安装的包（conda中不可用或版本不匹配）
  - pip
  - pip:
    # YOLO相关（conda中版本可能不是最新）
    - ultralytics>=8.0.0
    
    # 数据增强（conda版本可能较旧）
    - albumentations>=1.3.0
    
    # 路径处理工具
    - pathlib2>=2.3.7

# 可选的开发依赖（取消注释以包含）
# dev_dependencies:
#   - jupyter
#   - ipykernel
#   - pytest
#   - black
#   - flake8

# 环境变量设置（可选）
# variables:
#   - CUDA_VISIBLE_DEVICES: "0"
#   - PYTHONPATH: "${CONDA_PREFIX}/src"
