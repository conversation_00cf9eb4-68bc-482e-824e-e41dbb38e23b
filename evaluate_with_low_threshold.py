#!/usr/bin/env python3
"""
使用低置信度阈值重新评估现有模型
可能现有模型已经学到了少数类别，只是置信度阈值太高
"""

import os
import sys
import yaml
import torch
import json
from pathlib import Path

# 添加路径
sys.path.append('.')
sys.path.append('src')

from src.training.evaluate_multimodal import MultimodalEvaluator

def evaluate_with_multiple_thresholds(model_path, data_dir='data', output_dir='runs/eval_thresholds'):
    """使用多个置信度阈值评估模型"""
    print("🔍 使用多个置信度阈值评估模型...")
    
    # 置信度阈值列表
    conf_thresholds = [0.01, 0.05, 0.1, 0.15, 0.2, 0.25, 0.3, 0.4, 0.5]
    
    # 基础配置
    config = {
        'data_dir': data_dir,
        'batch_size': 8,
        'img_size': 640,
        'num_workers': 4,
        'model': {
            'fusion_type': 'cross_attention'
        }
    }
    
    results = {}
    
    for conf_thresh in conf_thresholds:
        print(f"\n📊 评估置信度阈值: {conf_thresh}")
        
        try:
            # 创建评估器
            evaluator = MultimodalEvaluator(config, model_path)
            
            # 评估
            metrics = evaluator.evaluate(conf_thresh=conf_thresh, iou_thresh=0.5)
            
            # 保存结果
            results[conf_thresh] = {
                'mAP': metrics['mAP'],
                'AP_per_class': metrics['AP_per_class'],
                'precision_per_class': metrics.get('precision_per_class', {}),
                'recall_per_class': metrics.get('recall_per_class', {})
            }
            
            # 打印关键指标
            print(f"  mAP: {metrics['mAP']:.4f}")
            ap_per_class = metrics['AP_per_class']
            
            # 重点关注之前AP为0的类别
            problem_classes = ['Glass_Loss', 'Polyme_Dirty', 'insulator']
            for class_name in problem_classes:
                if class_name in ap_per_class:
                    ap = ap_per_class[class_name]
                    print(f"  {class_name}: {ap:.4f}")
            
        except Exception as e:
            print(f"  ❌ 评估失败: {e}")
            results[conf_thresh] = None
    
    # 保存所有结果
    os.makedirs(output_dir, exist_ok=True)
    results_file = os.path.join(output_dir, 'threshold_analysis.json')
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 结果已保存到: {results_file}")
    
    # 分析最佳阈值
    analyze_best_thresholds(results)
    
    return results

def analyze_best_thresholds(results):
    """分析最佳置信度阈值"""
    print("\n📈 最佳阈值分析:")
    
    # 找到每个类别的最佳阈值
    class_names = ['110_two_hight_glass', 'Glass_Dirty', 'Glass_Loss', 'Polyme_Dirty', 'insulator']
    
    for class_name in class_names:
        best_thresh = None
        best_ap = 0.0
        
        for thresh, result in results.items():
            if result and class_name in result['AP_per_class']:
                ap = result['AP_per_class'][class_name]
                if ap > best_ap:
                    best_ap = ap
                    best_thresh = thresh
        
        if best_thresh is not None:
            print(f"  {class_name}: 最佳阈值 {best_thresh}, AP {best_ap:.4f}")
        else:
            print(f"  {class_name}: 未找到有效检测")
    
    # 找到整体最佳阈值
    best_overall_thresh = None
    best_overall_map = 0.0
    
    for thresh, result in results.items():
        if result:
            map_val = result['mAP']
            if map_val > best_overall_map:
                best_overall_map = map_val
                best_overall_thresh = thresh
    
    if best_overall_thresh is not None:
        print(f"\n🎯 整体最佳阈值: {best_overall_thresh}, mAP: {best_overall_map:.4f}")
        
        # 显示最佳阈值下的详细结果
        best_result = results[best_overall_thresh]
        print(f"\n📊 最佳阈值({best_overall_thresh})下的详细结果:")
        for class_name, ap in best_result['AP_per_class'].items():
            print(f"  {class_name}: {ap:.4f}")

def quick_evaluate_existing_model():
    """快速评估现有的最佳模型"""
    print("🚀 快速评估现有模型...")
    
    # 查找最新的训练结果
    possible_paths = [
        'runs/train_curriculum/*/weights/best.pt',
        'runs/train_optimized/*/weights/best.pt',
        'runs/train_clean_dataset/*/weights/best.pt'
    ]
    
    model_path = None
    for pattern in possible_paths:
        import glob
        matches = glob.glob(pattern)
        if matches:
            # 选择最新的
            model_path = max(matches, key=os.path.getctime)
            break
    
    if not model_path:
        print("❌ 未找到训练好的模型")
        return
    
    print(f"📁 使用模型: {model_path}")
    
    # 使用低置信度阈值快速评估
    config = {
        'data_dir': 'data',
        'batch_size': 8,
        'img_size': 640,
        'num_workers': 4,
        'model': {
            'fusion_type': 'cross_attention'
        }
    }
    
    print(f"\n🔍 使用置信度阈值 0.05 评估...")
    try:
        evaluator = MultimodalEvaluator(config, model_path)
        metrics = evaluator.evaluate(conf_thresh=0.05, iou_thresh=0.5)
        
        print(f"mAP: {metrics['mAP']:.4f}")
        print("各类别AP:")
        for class_name, ap in metrics['AP_per_class'].items():
            print(f"  {class_name}: {ap:.4f}")
        
        # 检查是否有改善
        problem_classes = ['Glass_Loss', 'Polyme_Dirty', 'insulator']
        improved = False
        for class_name in problem_classes:
            if class_name in metrics['AP_per_class']:
                ap = metrics['AP_per_class'][class_name]
                if ap > 0.01:  # 如果AP > 1%，说明有改善
                    improved = True
                    print(f"✅ {class_name} 有改善: {ap:.4f}")
        
        if improved:
            print("\n🎉 降低置信度阈值后有改善！建议:")
            print("  1. 使用更低的置信度阈值进行推理")
            print("  2. 调整模型的置信度分支训练")
        else:
            print("\n⚠️ 降低置信度阈值后仍无改善，需要重新训练")
            
    except Exception as e:
        print(f"❌ 评估失败: {e}")

def main():
    print("🔍 低置信度阈值评估工具")
    print("=" * 50)
    print("目标：检查现有模型是否已学到少数类别特征")
    print("方法：使用多个低置信度阈值重新评估")
    print("=" * 50)
    
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument('--model_path', type=str, help='模型路径')
    parser.add_argument('--quick', action='store_true', help='快速评估最新模型')
    parser.add_argument('--full_analysis', action='store_true', help='完整阈值分析')
    
    args = parser.parse_args()
    
    if args.quick:
        quick_evaluate_existing_model()
    elif args.full_analysis:
        if not args.model_path:
            print("❌ 完整分析需要指定模型路径")
            return
        evaluate_with_multiple_thresholds(args.model_path)
    else:
        # 默认快速评估
        quick_evaluate_existing_model()

if __name__ == '__main__':
    main()
