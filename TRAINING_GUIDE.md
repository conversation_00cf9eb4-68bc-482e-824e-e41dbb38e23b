# 渐进式训练使用指南

## 🚀 新的训练方式

现在 `train.py` 已经完全封装了渐进式训练流程，您可以通过以下方式使用：

### 1. 完整渐进式训练（推荐）

```bash
python train.py --progressive
```

这将自动执行所有3个阶段的训练：
- **阶段1**: 训练主要类别 [0,1,3] (25轮)
- **阶段2**: 添加类别2 [0,1,2,3] (20轮)  
- **阶段3**: 全类别训练 [0,1,2,3,4] (35轮)

### 2. 单阶段训练

```bash
# 阶段1
python train.py --stage 1

# 阶段2（需要阶段1的权重）
python train.py --stage 2 --previous_weights runs/train_progressive/20250802_155747/stage1/weights/best.pt

# 阶段3（需要阶段2的权重）
python train.py --stage 3 --previous_weights runs/train_progressive/20250802_155747/stage2/weights/best.pt
```

### 3. 传统配置文件方式（向后兼容）

```bash
python train.py --config configs/config_optimized.yaml
```

## 🎯 为什么这样设计？

1. **自动化**: 一个命令完成所有阶段，无需手动干预
2. **灵活性**: 可以选择执行单个阶段或完整流程
3. **可追溯**: 每个阶段的输出都有明确的路径和时间戳
4. **错误恢复**: 如果某个阶段失败，可以从该阶段重新开始

## 📊 输出结构

```
runs/train_progressive/20250802_155747/
├── stage1/
│   ├── weights/
│   │   └── best.pt
│   └── logs/
├── stage2/
│   ├── weights/
│   │   └── best.pt
│   └── logs/
└── stage3/
    ├── weights/
    │   └── best.pt  # 最终模型
    └── logs/
```

## 🔧 参数说明

- `--progressive`: 执行完整的渐进式训练
- `--stage N`: 执行指定阶段 (1, 2, 或 3)
- `--previous_weights PATH`: 指定前一阶段的权重文件
- `--epochs N`: 覆盖默认的训练轮数
- `--batch_size N`: 覆盖默认的批次大小
- `--lr FLOAT`: 覆盖默认的学习率

## 📈 预期效果

通过渐进式训练，您应该看到：
- 训练过程更加稳定
- 少数类别检测性能显著提升
- 整体mAP从0.15提升到0.6+
