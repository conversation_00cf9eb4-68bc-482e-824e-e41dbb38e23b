# 基于清理后数据集的优化训练配置
# 针对单目标检测的专门优化

# === 核心训练参数 ===
data_dir: "data"
output_dir: "runs/train_clean_dataset"
nc: 5  # 删除了无样本的broken disc和pollution-flashover
img_size: 512
batch_size: 8  # 提高批次大小，因为样本更干净
epochs: 100
num_workers: 0
grad_clip: 10.0

# === 模型配置 (简化融合策略) ===
model:
  type: "simple"
  fusion_type: "early"  # 简单融合，减少复杂度

# === 优化器配置 ===
optimizer:
  type: "Adam"
  lr: 0.001  # 稍微提高学习率，因为数据更清洁
  weight_decay: 0.0001

# === 学习率调度 ===
scheduler:
  type: "CosineAnnealingLR"
  T_max: 100
  eta_min: 0.00005

# === 早停配置 ===
early_stopping:
  patience: 20
  min_delta: 0.001

# === 损失函数权重调整 ===
loss_weights:
  box_loss: 1.0
  obj_loss: 2.0      # 提高置信度损失权重
  cls_loss: 0.8      # 适中的分类损失权重

# === 高级配置 ===
use_mixed_precision: true
use_ema: true

# === 数据增强 ===
augmentation:
  brightness_contrast: 0.2
  blur_prob: 0.1
  flip_prob: 0.5
  color_jitter: 0.15
  rotate_prob: 0.05
  scale_range: [0.9, 1.1]

# === 类别平衡策略 ===
class_balancing:
  use_focal_loss: true
  alpha: 0.25
  gamma: 2.0

# === 评估配置 ===
evaluation:
  conf_thresh: 0.1    # 降低置信度阈值
  iou_thresh: 0.5
  save_best: true
  
# === 日志配置 ===
logging:
  log_interval: 10
  save_interval: 10
  
# === 清理后数据集统计 ===
dataset_info:
  train_samples: 553
  valid_samples: 165
  test_samples: 85
  total_samples: 803
  removed_samples: 2025
  cleanup_date: "2025-01-30"