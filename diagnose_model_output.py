#!/usr/bin/env python3
"""
诊断模型输出问题
分析模型是否产生有效的预测结果
"""

import os
import sys
import torch
import numpy as np
import argparse

# 添加路径
sys.path.append('.')
sys.path.append('src')

def load_minimal_model(model_path, device='cuda'):
    """加载极简模型"""
    from src.models.minimal_multimodal import MinimalMultimodalYOLO
    
    model = MinimalMultimodalYOLO(nc=5)
    checkpoint = torch.load(model_path, map_location='cpu')
    
    if 'model_state_dict' in checkpoint:
        state_dict = checkpoint['model_state_dict']
    else:
        state_dict = checkpoint
    
    model.load_state_dict(state_dict)
    model = model.to(device)
    model.eval()
    
    return model

def analyze_model_output(model, device='cuda'):
    """分析模型输出"""
    print("🔍 [ANALYZE] 分析模型输出...")
    
    # 创建测试输入
    rgb_input = torch.randn(1, 3, 640, 640).to(device)
    thermal_input = torch.randn(1, 1, 640, 640).to(device)
    
    with torch.no_grad():
        outputs = model(rgb_input, thermal_input)
    
    print(f"📊 [OUTPUT] 输出数量: {len(outputs)}")
    
    for i, output in enumerate(outputs):
        print(f"\n📊 [OUTPUT {i}] 分析:")
        print(f"  形状: {output.shape}")
        print(f"  数据类型: {output.dtype}")
        print(f"  设备: {output.device}")
        print(f"  数值范围: [{output.min().item():.6f}, {output.max().item():.6f}]")
        print(f"  均值: {output.mean().item():.6f}")
        print(f"  标准差: {output.std().item():.6f}")
        
        # 分析输出结构
        if len(output.shape) == 4:  # [batch, channels, height, width]
            batch, channels, height, width = output.shape
            print(f"  批次大小: {batch}")
            print(f"  通道数: {channels}")
            print(f"  特征图尺寸: {height}x{width}")
            
            # 分析每个通道
            if channels == 10:  # nc(5) + 5 = 10
                print(f"  预期格式: [x, y, w, h, obj_conf, cls0, cls1, cls2, cls3, cls4]")
                
                # 分析置信度通道
                obj_conf_raw = output[0, 4, :, :]  # 原始置信度
                obj_conf_sigmoid = torch.sigmoid(obj_conf_raw)
                
                print(f"  原始置信度范围: [{obj_conf_raw.min().item():.6f}, {obj_conf_raw.max().item():.6f}]")
                print(f"  Sigmoid置信度范围: [{obj_conf_sigmoid.min().item():.6f}, {obj_conf_sigmoid.max().item():.6f}]")
                print(f"  Sigmoid置信度均值: {obj_conf_sigmoid.mean().item():.6f}")
                
                # 分析类别预测
                cls_logits = output[0, 5:, :, :]  # 类别logits
                cls_probs = torch.softmax(cls_logits, dim=0)
                
                print(f"  类别logits范围: [{cls_logits.min().item():.6f}, {cls_logits.max().item():.6f}]")
                print(f"  类别概率范围: [{cls_probs.min().item():.6f}, {cls_probs.max().item():.6f}]")
                
                # 计算最终置信度
                max_cls_prob, _ = cls_probs.max(dim=0)
                final_conf = obj_conf_sigmoid * max_cls_prob
                
                print(f"  最终置信度范围: [{final_conf.min().item():.6f}, {final_conf.max().item():.6f}]")
                print(f"  最终置信度均值: {final_conf.mean().item():.6f}")
                
                # 统计不同阈值下的预测数量
                thresholds = [0.001, 0.005, 0.01, 0.05, 0.1, 0.25]
                for thresh in thresholds:
                    count = (final_conf > thresh).sum().item()
                    total = final_conf.numel()
                    percentage = count / total * 100
                    print(f"  阈值 {thresh}: {count}/{total} ({percentage:.2f}%)")
    
    return outputs

def test_different_inputs(model, device='cuda'):
    """测试不同输入对模型输出的影响"""
    print("\n🧪 [TEST] 测试不同输入...")
    
    test_cases = [
        ("随机输入", torch.randn(1, 3, 640, 640), torch.randn(1, 1, 640, 640)),
        ("零输入", torch.zeros(1, 3, 640, 640), torch.zeros(1, 1, 640, 640)),
        ("全1输入", torch.ones(1, 3, 640, 640), torch.ones(1, 1, 640, 640)),
        ("正态分布输入", torch.randn(1, 3, 640, 640) * 0.5 + 0.5, torch.randn(1, 1, 640, 640) * 0.5 + 0.5)
    ]
    
    for name, rgb_input, thermal_input in test_cases:
        print(f"\n📊 [TEST] {name}:")
        
        rgb_input = rgb_input.to(device)
        thermal_input = thermal_input.to(device)
        
        with torch.no_grad():
            outputs = model(rgb_input, thermal_input)
        
        output = outputs[0]  # 只分析第一个输出
        
        # 计算最终置信度
        obj_conf = torch.sigmoid(output[0, 4, :, :])
        cls_logits = output[0, 5:, :, :]
        cls_probs = torch.softmax(cls_logits, dim=0)
        max_cls_prob, _ = cls_probs.max(dim=0)
        final_conf = obj_conf * max_cls_prob
        
        print(f"  最终置信度: 均值={final_conf.mean().item():.6f}, 最大值={final_conf.max().item():.6f}")
        
        # 统计高置信度预测
        high_conf_count = (final_conf > 0.01).sum().item()
        print(f"  高置信度(>0.01)预测数: {high_conf_count}")

def check_model_weights(model):
    """检查模型权重"""
    print("\n🔍 [WEIGHTS] 检查模型权重...")
    
    total_params = 0
    zero_params = 0
    
    for name, param in model.named_parameters():
        param_count = param.numel()
        zero_count = (param == 0).sum().item()
        
        total_params += param_count
        zero_params += zero_count
        
        print(f"  {name}: 形状={list(param.shape)}, 零值比例={zero_count/param_count*100:.1f}%")
        print(f"    范围=[{param.min().item():.6f}, {param.max().item():.6f}], 均值={param.mean().item():.6f}")
    
    print(f"\n📊 [SUMMARY] 权重统计:")
    print(f"  总参数数: {total_params:,}")
    print(f"  零值参数: {zero_params:,} ({zero_params/total_params*100:.1f}%)")

def main():
    parser = argparse.ArgumentParser(description='诊断模型输出问题')
    parser.add_argument('--model_path', type=str, required=True, help='模型路径')
    parser.add_argument('--device', type=str, default='cuda', help='设备')
    
    args = parser.parse_args()
    
    print("🔍 模型输出诊断")
    print("=" * 50)
    print(f"📁 模型路径: {args.model_path}")
    print(f"🖥️ 设备: {args.device}")
    print("=" * 50)
    
    if not os.path.exists(args.model_path):
        print(f"❌ [ERROR] 模型文件不存在: {args.model_path}")
        return
    
    device = args.device if torch.cuda.is_available() else 'cpu'
    print(f"🖥️ [DEVICE] 实际使用设备: {device}")
    
    try:
        # 加载模型
        model = load_minimal_model(args.model_path, device)
        print("✅ [SUCCESS] 模型加载成功")
        
        # 检查模型权重
        check_model_weights(model)
        
        # 分析模型输出
        outputs = analyze_model_output(model, device)
        
        # 测试不同输入
        test_different_inputs(model, device)
        
        print(f"\n💡 [DIAGNOSIS] 诊断结论:")
        
        # 分析第一个输出
        output = outputs[0]
        obj_conf = torch.sigmoid(output[0, 4, :, :])
        max_conf = obj_conf.max().item()
        mean_conf = obj_conf.mean().item()
        
        if max_conf < 0.001:
            print("❌ 问题: 模型置信度极低，可能训练不充分")
            print("💡 建议: 1. 检查训练过程 2. 降低置信度阈值到0.0001 3. 重新训练")
        elif max_conf < 0.01:
            print("⚠️ 问题: 模型置信度较低")
            print("💡 建议: 1. 使用更低的置信度阈值(0.001-0.005) 2. 检查训练收敛情况")
        elif max_conf < 0.1:
            print("✅ 模型置信度正常，但可能需要调整阈值")
            print("💡 建议: 使用置信度阈值0.01-0.05")
        else:
            print("✅ 模型置信度良好")
        
        print(f"\n🎯 [RECOMMENDATION] 推荐评估设置:")
        print(f"  置信度阈值: {max(0.001, max_conf * 0.1):.4f}")
        print(f"  IoU阈值: 0.5")
        
    except Exception as e:
        print(f"❌ [ERROR] 诊断失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
