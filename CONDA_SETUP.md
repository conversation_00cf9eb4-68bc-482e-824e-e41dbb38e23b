# Conda环境设置指南

## 📋 可用的环境配置文件

我已经将 `requirements/requirements.txt` 转换为多个conda兼容的环境配置文件：

### 1. 通用配置文件
- **`environment.yml`** - 通用配置，支持多种CUDA版本

### 2. 专用配置文件
- **`environment-cuda118.yml`** - 专门针对CUDA 11.8
- **`environment-cuda121.yml`** - 专门针对CUDA 12.1  
- **`environment-cpu.yml`** - CPU版本（无GPU支持）

## 🚀 使用方法

### 步骤1：检查您的CUDA版本
```bash
# 检查CUDA版本
nvidia-smi

# 或者
nvcc --version
```

### 步骤2：选择合适的环境文件

#### 如果您有CUDA 11.8：
```bash
conda env create -f environment-cuda118.yml
conda activate multimodal-cuda118
```

#### 如果您有CUDA 12.1：
```bash
conda env create -f environment-cuda121.yml
conda activate multimodal-cuda121
```

#### 如果您只有CPU（无GPU）：
```bash
conda env create -f environment-cpu.yml
conda activate multimodal-cpu
```

#### 使用通用配置（推荐）：
```bash
conda env create -f environment.yml
conda activate multimodal-detection
```

### 步骤3：验证安装
```bash
# 验证PyTorch安装
python -c "import torch; print(f'PyTorch版本: {torch.__version__}')"

# 验证CUDA支持（如果使用GPU版本）
python -c "import torch; print(f'CUDA可用: {torch.cuda.is_available()}')"

# 验证其他关键包
python -c "import cv2, ultralytics, albumentations; print('所有包安装成功')"
```

## 🔄 环境管理命令

### 更新环境
```bash
# 更新现有环境
conda env update -f environment.yml --prune
```

### 导出当前环境
```bash
# 导出当前环境配置
conda env export > my-environment.yml
```

### 删除环境
```bash
# 删除环境
conda env remove -n multimodal-detection
```

### 列出所有环境
```bash
# 查看所有conda环境
conda env list
```

## 📦 主要依赖包说明

### 通过conda安装的包：
- **pytorch, torchvision, torchaudio** - 深度学习框架
- **numpy, pandas** - 数据处理
- **opencv, pillow** - 图像处理
- **matplotlib, seaborn** - 可视化
- **scikit-learn** - 机器学习工具
- **tensorboard** - 训练监控

### 通过pip安装的包：
- **ultralytics** - YOLO框架
- **albumentations** - 数据增强
- **pathlib2** - 路径处理工具

## ⚠️ 注意事项

1. **CUDA版本兼容性**：确保选择与您的GPU驱动兼容的CUDA版本
2. **内存要求**：深度学习训练需要足够的GPU内存（建议8GB+）
3. **磁盘空间**：完整环境大约需要5-8GB磁盘空间
4. **网络连接**：首次安装需要下载大量包，确保网络稳定

## 🐛 常见问题解决

### 问题1：CUDA版本不匹配
```bash
# 解决方案：重新安装正确的PyTorch版本
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia
```

### 问题2：包冲突
```bash
# 解决方案：清理并重新创建环境
conda env remove -n multimodal-detection
conda env create -f environment.yml
```

### 问题3：pip包安装失败
```bash
# 解决方案：手动安装pip包
conda activate multimodal-detection
pip install ultralytics albumentations pathlib2
```

## 📞 技术支持

如果遇到环境配置问题，请：
1. 检查CUDA版本和GPU驱动
2. 确保conda版本是最新的：`conda update conda`
3. 尝试使用CPU版本进行测试
4. 查看详细错误日志进行排查
