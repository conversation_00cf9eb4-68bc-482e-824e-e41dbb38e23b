# CPU版本依赖文件 - 适用于没有NVIDIA GPU的用户
# 安装命令: pip install -r requirements_cpu.txt

# 深度学习框架 - CPU版本
--index-url https://download.pytorch.org/whl/cpu
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0

# YOLO相关（支持CPU）
ultralytics>=8.0.0

# 图像处理
opencv-python>=4.8.0
Pillow>=9.5.0
albumentations>=1.3.0

# 数据处理
numpy>=1.24.0
pandas>=2.0.0

# 可视化
matplotlib>=3.7.0
seaborn>=0.12.0

# 机器学习工具
scikit-learn>=1.3.0

# 进度条
tqdm>=4.65.0

# 配置文件处理
PyYAML>=6.0

# TensorBoard支持
tensorboard>=2.13.0

# 其他工具
pathlib2>=2.3.7 