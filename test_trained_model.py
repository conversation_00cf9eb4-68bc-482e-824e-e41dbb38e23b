#!/usr/bin/env python3
"""
测试训练好的模型 - 使用训练时的实际模型架构
"""

import os
import sys
import torch
import numpy as np
import cv2

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)
sys.path.insert(0, current_dir)

# 使用训练时的模型类
from src.models.multimodal_yolo_fixed import FixedMultimodalYOLO as SimpleMultimodalYOLO

def load_trained_model(model_path):
    """加载训练好的模型"""
    print(f"🔧 加载训练好的模型: {model_path}")
    
    # 加载checkpoint
    checkpoint = torch.load(model_path, map_location='cpu')
    
    # 打印checkpoint内容
    print(f"📋 Checkpoint 包含的键: {list(checkpoint.keys())}")
    
    # 获取配置信息
    if 'config' in checkpoint:
        config = checkpoint['config']
        print(f"📋 训练配置: nc={config.get('nc', 5)}")
        nc = config.get('nc', 5)
    else:
        nc = 5
        print("⚠️ 未找到配置信息，使用默认nc=5")
    
    # 创建模型（使用训练时的架构）
    model = SimpleMultimodalYOLO(
        nc=nc,
        fusion_type='cross_attention'
    )
    
    # 加载模型权重
    if 'model_state_dict' in checkpoint:
        model_weights = checkpoint['model_state_dict']
    elif 'model' in checkpoint:
        model_weights = checkpoint['model']
    else:
        print("❌ 未找到模型权重")
        return None
    
    # 加载权重
    try:
        missing_keys, unexpected_keys = model.load_state_dict(model_weights, strict=False)
        print(f"✅ 模型权重加载成功")
        if missing_keys:
            print(f"⚠️ 缺失的权重键数量: {len(missing_keys)}")
        if unexpected_keys:
            print(f"⚠️ 意外的权重键数量: {len(unexpected_keys)}")
    except Exception as e:
        print(f"❌ 权重加载失败: {e}")
        return None
    
    model.eval()
    return model

def test_model_inference(model, test_image_path):
    """测试模型推理"""
    print(f"🖼️ 测试图像: {test_image_path}")
    
    # 加载和预处理图像
    try:
        # RGB图像
        rgb_img = cv2.imread(test_image_path)
        if rgb_img is None:
            print(f"❌ 无法加载图像: {test_image_path}")
            return
        
        rgb_img = cv2.cvtColor(rgb_img, cv2.COLOR_BGR2RGB)
        rgb_img = cv2.resize(rgb_img, (640, 640))
        rgb_tensor = torch.from_numpy(rgb_img).permute(2, 0, 1).float() / 255.0
        rgb_tensor = rgb_tensor.unsqueeze(0)
        
        # 热红外图像（单通道）
        thermal_path = test_image_path.replace('.jpg', '_thermal.png').replace('images', 'images/thermal')
        if os.path.exists(thermal_path):
            thermal_img = cv2.imread(thermal_path, cv2.IMREAD_GRAYSCALE)
        else:
            print(f"⚠️ 热红外图像不存在，使用零图像: {thermal_path}")
            thermal_img = np.zeros((640, 640), dtype=np.uint8)

        thermal_img = cv2.resize(thermal_img, (640, 640))
        thermal_tensor = torch.from_numpy(thermal_img).unsqueeze(0).float() / 255.0  # [H, W] -> [1, H, W]
        thermal_tensor = thermal_tensor.unsqueeze(0)  # [1, H, W] -> [1, 1, H, W]
        
        print("✅ 图像预处理完成")
        
    except Exception as e:
        print(f"❌ 图像预处理失败: {e}")
        return
    
    # 模型推理
    try:
        with torch.no_grad():
            outputs = model(rgb_tensor, thermal_tensor)
        
        print(f"📊 模型推理成功")
        print(f"  输出数量: {len(outputs)}")
        
        # 分析每个输出
        for i, output in enumerate(outputs):
            if isinstance(output, torch.Tensor):
                print(f"  输出{i}: 形状={output.shape}, 范围=[{output.min().item():.4f}, {output.max().item():.4f}]")
                
                # 如果是4D张量 [B, C, H, W]，分析置信度
                if len(output.shape) == 4:
                    b, c, h, w = output.shape
                    if c >= 5:  # 至少有x,y,w,h,conf
                        # 提取置信度通道（第5个通道，索引4）
                        conf_channel = output[0, 4, :, :]  # [H, W]
                        conf_sigmoid = torch.sigmoid(conf_channel)
                        
                        print(f"    置信度通道原始值: [{conf_channel.min().item():.4f}, {conf_channel.max().item():.4f}]")
                        print(f"    置信度Sigmoid后: [{conf_sigmoid.min().item():.6f}, {conf_sigmoid.max().item():.6f}]")
                        print(f"    置信度均值: {conf_sigmoid.mean().item():.6f}")
                        
                        # 统计不同阈值下的激活数量
                        total_pixels = h * w
                        for thresh in [0.1, 0.05, 0.01, 0.005, 0.001, 0.0001]:
                            count = (conf_sigmoid > thresh).sum().item()
                            percentage = count / total_pixels * 100
                            print(f"    置信度 > {thresh}: {count}/{total_pixels} ({percentage:.2f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型推理失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    model_path = "runs/train_progressive/20250802_170243/stage3/weights/best.pt"
    test_image = "data/test/images/100_PNG_jpg.rf.3511803ad7b07648ab43e7534941560f.jpg"
    
    print("🔍 训练模型测试开始...")
    print("-" * 60)
    
    # 检查文件
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return
    
    if not os.path.exists(test_image):
        print(f"❌ 测试图像不存在: {test_image}")
        return
    
    # 加载模型
    model = load_trained_model(model_path)
    if model is None:
        print("❌ 模型加载失败")
        return
    
    # 测试推理
    success = test_model_inference(model, test_image)
    
    if success:
        print("\n🎯 测试结论:")
        print("✅ 模型可以正常加载和推理")
        print("💡 如果置信度普遍很低，建议在评估时使用更低的阈值")
        print("💡 建议尝试: python evaluate.py --model_path [模型路径] --data_dir data --conf_thresh 0.0001")
    else:
        print("\n❌ 模型测试失败")

if __name__ == '__main__':
    main()
