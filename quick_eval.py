#!/usr/bin/env python3
"""
快速评估脚本 - 使用极低置信度阈值测试模型
"""

import os
import sys
import argparse

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)
sys.path.insert(0, current_dir)

def main():
    parser = argparse.ArgumentParser(description='快速模型评估')
    parser.add_argument('--model_path', type=str, required=True, help='模型权重路径')
    parser.add_argument('--data_dir', type=str, default='data', help='数据集目录')
    
    args = parser.parse_args()
    
    print("🚀 快速评估开始...")
    print(f"📁 模型路径: {args.model_path}")
    print(f"📁 数据目录: {args.data_dir}")
    print("-" * 50)
    
    # 测试不同的置信度阈值
    thresholds = [0.1, 0.05, 0.01, 0.005, 0.001, 0.0001]
    
    for thresh in thresholds:
        print(f"\n🔍 测试置信度阈值: {thresh}")
        
        # 构建评估命令
        cmd = f"python evaluate.py --model_path {args.model_path} --data_dir {args.data_dir} --conf_thresh {thresh}"
        print(f"执行命令: {cmd}")
        
        # 执行评估
        result = os.system(cmd)
        if result != 0:
            print(f"❌ 评估失败，返回码: {result}")
        else:
            print(f"✅ 评估完成")
        
        print("-" * 30)

if __name__ == '__main__':
    main()
