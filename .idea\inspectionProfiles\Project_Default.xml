<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="60">
            <item index="0" class="java.lang.String" itemvalue="torch-spline-conv" />
            <item index="1" class="java.lang.String" itemvalue="torch-scatter" />
            <item index="2" class="java.lang.String" itemvalue="attridict" />
            <item index="3" class="java.lang.String" itemvalue="torch-cluster" />
            <item index="4" class="java.lang.String" itemvalue="torch" />
            <item index="5" class="java.lang.String" itemvalue="torch-sparse" />
            <item index="6" class="java.lang.String" itemvalue="torch-geometric" />
            <item index="7" class="java.lang.String" itemvalue="omegaconf" />
            <item index="8" class="java.lang.String" itemvalue="PyYAML" />
            <item index="9" class="java.lang.String" itemvalue="ray" />
            <item index="10" class="java.lang.String" itemvalue="pandas" />
            <item index="11" class="java.lang.String" itemvalue="scipy" />
            <item index="12" class="java.lang.String" itemvalue="tensorflow" />
            <item index="13" class="java.lang.String" itemvalue="tslearn" />
            <item index="14" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="15" class="java.lang.String" itemvalue="matplotlib" />
            <item index="16" class="java.lang.String" itemvalue="numpy" />
            <item index="17" class="java.lang.String" itemvalue="torchvision" />
            <item index="18" class="java.lang.String" itemvalue="torchaudio" />
            <item index="19" class="java.lang.String" itemvalue="tqdm" />
            <item index="20" class="java.lang.String" itemvalue="seaborn" />
            <item index="21" class="java.lang.String" itemvalue="aiohttp" />
            <item index="22" class="java.lang.String" itemvalue="torch_geometric" />
            <item index="23" class="java.lang.String" itemvalue="pyinstaller" />
            <item index="24" class="java.lang.String" itemvalue="DrissionPage" />
            <item index="25" class="java.lang.String" itemvalue="colorama" />
            <item index="26" class="java.lang.String" itemvalue="python-dotenv" />
            <item index="27" class="java.lang.String" itemvalue="requests" />
            <item index="28" class="java.lang.String" itemvalue="torch_sparse" />
            <item index="29" class="java.lang.String" itemvalue="opencv-python" />
            <item index="30" class="java.lang.String" itemvalue="Pillow" />
            <item index="31" class="java.lang.String" itemvalue="pillow" />
            <item index="32" class="java.lang.String" itemvalue="selenium" />
            <item index="33" class="java.lang.String" itemvalue="webdriver_manager" />
            <item index="34" class="java.lang.String" itemvalue="tensorboard" />
            <item index="35" class="java.lang.String" itemvalue="pyyaml" />
            <item index="36" class="java.lang.String" itemvalue="opacus" />
            <item index="37" class="java.lang.String" itemvalue="networkx" />
            <item index="38" class="java.lang.String" itemvalue="pytest" />
            <item index="39" class="java.lang.String" itemvalue="pytest-cov" />
            <item index="40" class="java.lang.String" itemvalue="dgl" />
            <item index="41" class="java.lang.String" itemvalue="wandb" />
            <item index="42" class="java.lang.String" itemvalue="black" />
            <item index="43" class="java.lang.String" itemvalue="plotly" />
            <item index="44" class="java.lang.String" itemvalue="flake8" />
            <item index="45" class="java.lang.String" itemvalue="torchmetrics" />
            <item index="46" class="java.lang.String" itemvalue="joblib" />
            <item index="47" class="java.lang.String" itemvalue="threadpoolctl" />
            <item index="48" class="java.lang.String" itemvalue="isodate" />
            <item index="49" class="java.lang.String" itemvalue="rdflib" />
            <item index="50" class="java.lang.String" itemvalue="kiwisolver" />
            <item index="51" class="java.lang.String" itemvalue="cycler" />
            <item index="52" class="java.lang.String" itemvalue="contourpy" />
            <item index="53" class="java.lang.String" itemvalue="psutil" />
            <item index="54" class="java.lang.String" itemvalue="jinja2" />
            <item index="55" class="java.lang.String" itemvalue="pytorch-lightning" />
            <item index="56" class="java.lang.String" itemvalue="fonttools" />
            <item index="57" class="java.lang.String" itemvalue="certifi" />
            <item index="58" class="java.lang.String" itemvalue="urllib3" />
            <item index="59" class="java.lang.String" itemvalue="pyparsing" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="Stylelint" enabled="true" level="ERROR" enabled_by_default="true" />
  </profile>
</component>