# CPU模式优化配置文件 - 适用于没有GPU的用户

# 数据配置
data_dir: "data"  # 数据集根目录
output_dir: "runs/train_cpu"  # 输出目录
nc: 5  # 类别数量（删除了无样本的broken disc和pollution-flashover）
img_size: 416  # 输入图像尺寸（CPU模式使用较小尺寸）
batch_size: 2  # 批次大小（CPU模式使用较小批次）
epochs: 50  # 训练轮数（CPU模式建议减少轮数做快速验证）
num_workers: 0  # 数据加载器工作进程数（Windows/CPU模式必须设为0）
grad_clip: 10.0  # 梯度裁剪阈值

# 模型配置
model:
  type: "simple"  # 模型类型
  yolo_model_path: "yolov8n.pt"  # 使用最小的YOLOv8模型
  fusion_type: "cross_attention"  # 特征融合类型

# 优化器配置
optimizer:
  type: "Adam"  # 优化器类型
  lr: 0.0005  # 学习率（CPU模式使用较小学习率）
  weight_decay: 0.0001  # 权重衰减

# 学习率调度器配置
scheduler:
  type: "StepLR"  # 使用简单的步进调度器
  step_size: 15  # 每15轮降低学习率
  gamma: 0.5  # 学习率衰减因子

# 早停配置
early_stopping:
  patience: 5  # CPU模式使用较小的patience

# 训练技巧（CPU模式关闭高级功能）
use_mixed_precision: false  # CPU不支持混合精度
use_ema: false  # 关闭指数移动平均以节省内存

# 说明：
# CPU模式训练会比较慢，建议：
# 1. 先用小数据集验证流程
# 2. 减小图像尺寸和批次大小
# 3. 考虑使用云GPU或租用GPU服务器进行正式训练 