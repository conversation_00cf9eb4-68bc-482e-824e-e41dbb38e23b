#!/usr/bin/env python3
"""
CUDA环境诊断脚本
帮助用户检查CUDA安装和配置状态
"""

import sys

# 添加src目录到Python路径
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import subprocess
import platform

def check_system_info():
    """检查系统信息"""
    print("=== 系统信息 ===")
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"Python版本: {sys.version}")
    print(f"架构: {platform.machine()}")
    print()

def check_nvidia_gpu():
    """检查NVIDIA GPU"""
    print("=== GPU检查 ===")
    try:
        # 检查nvidia-smi命令
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✓ NVIDIA GPU检测到:")
            lines = result.stdout.split('\n')
            for line in lines:
                if 'GeForce' in line or 'RTX' in line or 'GTX' in line or 'Tesla' in line or 'Quadro' in line:
                    print(f"  {line.strip()}")
            print()
            return True
        else:
            print("✗ nvidia-smi命令失败")
            print("可能原因: 没有NVIDIA GPU或驱动未安装")
            return False
    except FileNotFoundError:
        print("✗ nvidia-smi命令未找到")
        print("可能原因: NVIDIA驱动未安装或PATH环境变量未配置")
        return False
    except subprocess.TimeoutExpired:
        print("✗ nvidia-smi命令超时")
        return False
    except Exception as e:
        print(f"✗ 检查GPU时出错: {e}")
        return False

def check_cuda_toolkit():
    """检查CUDA Toolkit"""
    print("=== CUDA Toolkit检查 ===")
    try:
        # 检查nvcc命令
        result = subprocess.run(['nvcc', '--version'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✓ CUDA Toolkit已安装:")
            for line in result.stdout.split('\n'):
                if 'release' in line.lower():
                    print(f"  {line.strip()}")
            print()
            return True
        else:
            print("✗ nvcc命令失败")
            return False
    except FileNotFoundError:
        print("✗ nvcc命令未找到")
        print("可能原因: CUDA Toolkit未安装或PATH环境变量未配置")
        return False
    except Exception as e:
        print(f"✗ 检查CUDA Toolkit时出错: {e}")
        return False

def check_pytorch():
    """检查PyTorch CUDA支持"""
    print("=== PyTorch CUDA支持检查 ===")
    try:
        import torch
        print(f"PyTorch版本: {torch.__version__}")
        print(f"CUDA可用: {'是' if torch.cuda.is_available() else '否'}")
        
        if torch.cuda.is_available():
            print(f"CUDA版本: {torch.version.cuda}")
            print(f"cuDNN版本: {torch.backends.cudnn.version()}")
            print(f"GPU数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
            print("✓ PyTorch CUDA支持正常")
            return True
        else:
            print("✗ PyTorch无法使用CUDA")
            
            # 检查PyTorch是否为CPU版本
            if '+cpu' in torch.__version__:
                print("⚠️ 检测到CPU版本的PyTorch")
                print("需要重新安装GPU版本的PyTorch")
            
            return False
            
    except ImportError:
        print("✗ PyTorch未安装")
        return False
    except Exception as e:
        print(f"✗ 检查PyTorch时出错: {e}")
        return False

def check_environment_variables():
    """检查环境变量"""
    print("=== 环境变量检查 ===")
    import os
    
    important_vars = ['CUDA_HOME', 'CUDA_PATH', 'PATH', 'LD_LIBRARY_PATH']
    
    for var in important_vars:
        value = os.environ.get(var)
        if value:
            if var == 'PATH':
                cuda_paths = [p for p in value.split(os.pathsep) if 'cuda' in p.lower()]
                if cuda_paths:
                    print(f"✓ {var} 包含CUDA路径:")
                    for path in cuda_paths[:3]:  # 只显示前3个
                        print(f"  {path}")
                else:
                    print(f"⚠️ {var} 不包含CUDA路径")
            else:
                print(f"✓ {var}: {value}")
        else:
            print(f"✗ {var}: 未设置")
    print()

def provide_solutions():
    """提供解决方案"""
    print("=== 解决方案 ===")
    print("根据上述检查结果，请按照以下步骤操作：")
    print()
    
    print("1. 【硬件检查】")
    print("   确保您的电脑有NVIDIA显卡（不支持AMD显卡或集成显卡）")
    print()
    
    print("2. 【安装NVIDIA驱动】")
    print("   Windows: 从NVIDIA官网下载最新驱动")
    print("   Linux: sudo apt install nvidia-driver-xxx")
    print()
    
    print("3. 【安装CUDA Toolkit】")
    print("   从NVIDIA官网下载CUDA Toolkit 11.8或12.x版本")
    print("   https://developer.nvidia.com/cuda-downloads")
    print()
    
    print("4. 【重新安装PyTorch】")
    print("   卸载当前PyTorch: pip uninstall torch torchvision torchaudio")
    print()
    print("   安装GPU版本PyTorch:")
    print("   # CUDA 11.8")
    print("   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118")
    print()
    print("   # CUDA 12.1")
    print("   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121")
    print()
    
    print("5. 【验证安装】")
    print("   重新运行此脚本检查CUDA是否可用")
    print()
    
    print("6. 【常见问题】")
    print("   - 如果是笔记本电脑，确保使用独立显卡而不是集成显卡")
    print("   - 重启电脑后再次测试")
    print("   - 检查CUDA版本与PyTorch版本的兼容性")

def main():
    print("🔧 CUDA环境诊断工具")
    print("=" * 50)
    
    # 检查系统信息
    check_system_info()
    
    # 检查各个组件
    gpu_ok = check_nvidia_gpu()
    cuda_ok = check_cuda_toolkit()
    pytorch_ok = check_pytorch()
    
    # 检查环境变量
    check_environment_variables()
    
    # 总结结果
    print("=" * 50)
    print("📊 诊断结果总结:")
    print(f"  NVIDIA GPU: {'✓' if gpu_ok else '✗'}")
    print(f"  CUDA Toolkit: {'✓' if cuda_ok else '✗'}")
    print(f"  PyTorch CUDA: {'✓' if pytorch_ok else '✗'}")
    print()
    
    if all([gpu_ok, cuda_ok, pytorch_ok]):
        print("🎉 CUDA环境配置完整！")
    else:
        print("⚠️ CUDA环境存在问题，请参考以下解决方案：")
        print()
        provide_solutions()

if __name__ == '__main__':
    main() 