#!/usr/bin/env python3
"""
快速验证标签格式对训练的影响
"""

import os
import numpy as np

def analyze_label_impact():
    """分析混合标签格式的影响"""
    
    # 读取一个多边形格式的标签文件
    label_file = "data/test/labels/su110kv_vo-805-_jpg.rf.559cb191cd2a7c09723d063ff76f371e.txt"
    
    with open(label_file, 'r') as f:
        line = f.readline().strip()
    
    parts = line.split()
    print("🔍 多边形格式标签分析:")
    print(f"原始标签: {line}")
    print()
    
    # 数据加载器读取的内容
    class_id = int(parts[0])
    x_center = float(parts[1])
    y_center = float(parts[2]) 
    width = float(parts[3])
    height = float(parts[4])
    
    print("📊 数据加载器解析为:")
    print(f"类别ID: {class_id}")
    print(f"中心X: {x_center:.6f}")
    print(f"中心Y: {y_center:.6f}")
    print(f"宽度: {width:.6f}")
    print(f"高度: {height:.6f}")
    print()
    
    # 计算真实的多边形边界框
    coords = [float(x) for x in parts[1:]]
    x_coords = coords[0::2]  # 所有x坐标
    y_coords = coords[1::2]  # 所有y坐标
    
    real_x_min = min(x_coords)
    real_x_max = max(x_coords)
    real_y_min = min(y_coords)
    real_y_max = max(y_coords)
    
    real_width = real_x_max - real_x_min
    real_height = real_y_max - real_y_min
    real_center_x = real_x_min + real_width / 2
    real_center_y = real_y_min + real_height / 2
    
    print("✅ 真实的边界框应该是:")
    print(f"中心X: {real_center_x:.6f}")
    print(f"中心Y: {real_center_y:.6f}")
    print(f"宽度: {real_width:.6f}")
    print(f"高度: {real_height:.6f}")
    print()
    
    # 计算差异
    center_x_diff = abs(x_center - real_center_x)
    center_y_diff = abs(y_center - real_center_y)
    width_diff = abs(width - real_width)
    height_diff = abs(height - real_height)
    
    print("❌ 误差分析:")
    print(f"中心X误差: {center_x_diff:.6f}")
    print(f"中心Y误差: {center_y_diff:.6f}")
    print(f"宽度误差: {width_diff:.6f}")
    print(f"高度误差: {height_diff:.6f}")
    print()
    
    print("🎯 结论:")
    if center_x_diff > 0.1 or center_y_diff > 0.1 or width_diff > 0.1 or height_diff > 0.1:
        print("❌ 混合格式标签严重影响训练！")
        print("   数据加载器读取的标签与真实边界框差异巨大")
        print("   这解释了为什么评估指标全为0")
    else:
        print("✅ 标签格式影响较小")

if __name__ == '__main__':
    analyze_label_impact()