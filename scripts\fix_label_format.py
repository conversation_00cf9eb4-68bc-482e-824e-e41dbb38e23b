#!/usr/bin/env python3
"""
标签格式统一化脚本
将混合的多边形分割格式和YOLO格式标签统一转换为标准YOLO边界框格式

功能：
1. 检测标签文件格式（边界框 vs 多边形）
2. 将多边形标注转换为最小外接矩形
3. 统一为标准YOLO格式：class_id center_x center_y width height
4. 备份原始标签文件
5. 生成转换报告

作者：AI助手
日期：2025-01-30
"""

import os
import shutil
import numpy as np
import json
from pathlib import Path
from typing import List, Tuple, Dict
import argparse


class LabelFormatFixer:
    """标签格式修复器"""
    
    def __init__(self, data_dir: str, backup_dir: str = None):
        self.data_dir = Path(data_dir)
        self.backup_dir = Path(backup_dir) if backup_dir else self.data_dir / "labels_backup"
        
        # 统计信息
        self.stats = {
            'total_files': 0,
            'yolo_format': 0,
            'polygon_format': 0,
            'mixed_format': 0,
            'converted_files': 0,
            'conversion_errors': []
        }
    
    def detect_label_format(self, label_path: str) -> str:
        """
        检测标签文件格式
        
        Returns:
            'yolo': 标准YOLO格式 (5列)
            'polygon': 多边形格式 (>5列)
            'mixed': 混合格式
            'empty': 空文件
            'error': 格式错误
        """
        try:
            with open(label_path, 'r', encoding='utf-8') as f:
                lines = [line.strip() for line in f.readlines() if line.strip()]
            
            if not lines:
                return 'empty'
            
            formats = set()
            for line in lines:
                parts = line.split()
                if len(parts) < 5:
                    return 'error'
                elif len(parts) == 5:
                    formats.add('yolo')
                else:
                    formats.add('polygon')
            
            if len(formats) == 1:
                return list(formats)[0]
            else:
                return 'mixed'
                
        except Exception as e:
            print(f"读取文件 {label_path} 时出错: {e}")
            return 'error'
    
    def polygon_to_bbox(self, polygon_coords: List[float]) -> Tuple[float, float, float, float]:
        """
        将多边形坐标转换为边界框
        
        Args:
            polygon_coords: [x1, y1, x2, y2, ..., xn, yn]
            
        Returns:
            (center_x, center_y, width, height) 归一化坐标
        """
        # 确保坐标数量为偶数
        if len(polygon_coords) % 2 != 0:
            raise ValueError(f"多边形坐标数量必须为偶数，得到: {len(polygon_coords)}")
        
        # 提取x和y坐标
        x_coords = polygon_coords[0::2]  # 偶数索引
        y_coords = polygon_coords[1::2]  # 奇数索引
        
        # 计算边界框
        x_min = min(x_coords)
        x_max = max(x_coords)
        y_min = min(y_coords)
        y_max = max(y_coords)
        
        # 转换为YOLO格式 (center_x, center_y, width, height)
        width = x_max - x_min
        height = y_max - y_min
        center_x = x_min + width / 2
        center_y = y_min + height / 2
        
        # 确保坐标在[0,1]范围内
        center_x = max(0.0, min(1.0, center_x))
        center_y = max(0.0, min(1.0, center_y))
        width = max(0.0, min(1.0, width))
        height = max(0.0, min(1.0, height))
        
        return center_x, center_y, width, height
    
    def convert_label_file(self, label_path: str) -> bool:
        """
        转换单个标签文件
        
        Returns:
            True: 转换成功
            False: 转换失败
        """
        try:
            with open(label_path, 'r', encoding='utf-8') as f:
                lines = [line.strip() for line in f.readlines() if line.strip()]
            
            converted_lines = []
            has_conversion = False
            
            for line_num, line in enumerate(lines, 1):
                parts = line.split()
                
                if len(parts) < 5:
                    print(f"警告: {label_path} 第{line_num}行格式错误，跳过")
                    continue
                
                class_id = int(parts[0])
                coords = [float(x) for x in parts[1:]]
                
                if len(parts) == 5:
                    # 已经是YOLO格式，直接保留
                    converted_lines.append(line)
                else:
                    # 多边形格式，需要转换
                    has_conversion = True
                    try:
                        center_x, center_y, width, height = self.polygon_to_bbox(coords)
                        
                        # 验证转换结果
                        if width <= 0 or height <= 0:
                            print(f"警告: {label_path} 第{line_num}行转换后边界框无效，跳过")
                            continue
                        
                        # 格式化为YOLO格式
                        yolo_line = f"{class_id} {center_x:.6f} {center_y:.6f} {width:.6f} {height:.6f}"
                        converted_lines.append(yolo_line)
                        
                    except Exception as e:
                        print(f"错误: {label_path} 第{line_num}行转换失败: {e}")
                        self.stats['conversion_errors'].append(f"{label_path}:{line_num} - {str(e)}")
                        continue
            
            # 如果有转换，写入新文件
            if has_conversion and converted_lines:
                with open(label_path, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(converted_lines) + '\n')
                return True
            
            return False
            
        except Exception as e:
            print(f"处理文件 {label_path} 时出错: {e}")
            self.stats['conversion_errors'].append(f"{label_path} - {str(e)}")
            return False
    
    def backup_labels(self, splits: List[str] = ['train', 'valid', 'test']):
        """备份原始标签文件"""
        print("📦 正在备份原始标签文件...")
        
        # 创建备份目录
        self.backup_dir.mkdir(exist_ok=True)
        
        for split in splits:
            labels_dir = self.data_dir / split / 'labels'
            if not labels_dir.exists():
                print(f"⚠️ 标签目录不存在: {labels_dir}")
                continue
            
            backup_split_dir = self.backup_dir / split
            backup_split_dir.mkdir(exist_ok=True)
            
            # 复制标签文件
            label_files = list(labels_dir.glob('*.txt'))
            for label_file in label_files:
                backup_file = backup_split_dir / label_file.name
                shutil.copy2(label_file, backup_file)
            
            print(f"✅ 已备份 {len(label_files)} 个 {split} 标签文件到 {backup_split_dir}")
    
    def analyze_labels(self, splits: List[str] = ['train', 'valid', 'test']) -> Dict:
        """分析标签格式分布"""
        print("🔍 正在分析标签格式...")
        
        analysis = {}
        
        for split in splits:
            labels_dir = self.data_dir / split / 'labels'
            if not labels_dir.exists():
                print(f"⚠️ 标签目录不存在: {labels_dir}")
                continue
            
            split_stats = {
                'total': 0,
                'yolo': 0,
                'polygon': 0,
                'mixed': 0,
                'empty': 0,
                'error': 0,
                'files_by_format': {
                    'yolo': [],
                    'polygon': [],
                    'mixed': [],
                    'empty': [],
                    'error': []
                }
            }
            
            label_files = list(labels_dir.glob('*.txt'))
            for label_file in label_files:
                format_type = self.detect_label_format(str(label_file))
                split_stats['total'] += 1
                split_stats[format_type] += 1
                split_stats['files_by_format'][format_type].append(label_file.name)
            
            analysis[split] = split_stats
            
            print(f"📊 {split} 数据集分析:")
            print(f"  总文件数: {split_stats['total']}")
            print(f"  YOLO格式: {split_stats['yolo']}")
            print(f"  多边形格式: {split_stats['polygon']}")
            print(f"  混合格式: {split_stats['mixed']}")
            print(f"  空文件: {split_stats['empty']}")
            print(f"  错误格式: {split_stats['error']}")
            print()
        
        return analysis
    
    def fix_labels(self, splits: List[str] = ['train', 'valid', 'test']):
        """修复标签格式"""
        print("🔧 正在修复标签格式...")
        
        for split in splits:
            labels_dir = self.data_dir / split / 'labels'
            if not labels_dir.exists():
                print(f"⚠️ 标签目录不存在: {labels_dir}")
                continue
            
            print(f"🔄 处理 {split} 数据集...")
            
            label_files = list(labels_dir.glob('*.txt'))
            converted_count = 0
            
            for label_file in label_files:
                format_type = self.detect_label_format(str(label_file))
                
                # 只处理需要转换的格式
                if format_type in ['polygon', 'mixed']:
                    if self.convert_label_file(str(label_file)):
                        converted_count += 1
                        self.stats['converted_files'] += 1
                
                # 更新统计
                self.stats['total_files'] += 1
                if format_type == 'yolo':
                    self.stats['yolo_format'] += 1
                elif format_type == 'polygon':
                    self.stats['polygon_format'] += 1
                elif format_type == 'mixed':
                    self.stats['mixed_format'] += 1
            
            print(f"✅ {split} 数据集处理完成，转换了 {converted_count} 个文件")
            print()
    
    def generate_report(self) -> str:
        """生成转换报告"""
        report = f"""
标签格式转换报告
================
处理时间: {Path().cwd()}
数据目录: {self.data_dir}
备份目录: {self.backup_dir}

统计信息:
--------
总文件数: {self.stats['total_files']}
原YOLO格式: {self.stats['yolo_format']}
原多边形格式: {self.stats['polygon_format']}
原混合格式: {self.stats['mixed_format']}
成功转换: {self.stats['converted_files']}

转换错误:
--------
"""
        if self.stats['conversion_errors']:
            for error in self.stats['conversion_errors']:
                report += f"- {error}\n"
        else:
            report += "无转换错误\n"
        
        return report
    
    def save_report(self, output_path: str = None):
        """保存转换报告"""
        if output_path is None:
            output_path = self.data_dir / "label_conversion_report.txt"
        
        report = self.generate_report()
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"📋 转换报告已保存到: {output_path}")
        return report


def main():
    parser = argparse.ArgumentParser(description='修复标签格式脚本')
    parser.add_argument('--data_dir', type=str, default='data', help='数据集根目录')
    parser.add_argument('--backup_dir', type=str, help='备份目录 (默认: data/labels_backup)')
    parser.add_argument('--splits', nargs='+', default=['train', 'valid', 'test'], 
                       help='要处理的数据分割')
    parser.add_argument('--analyze_only', action='store_true', help='仅分析，不执行转换')
    parser.add_argument('--no_backup', action='store_true', help='跳过备份步骤')
    
    args = parser.parse_args()
    
    print("🚀 标签格式统一化工具启动")
    print("=" * 50)
    
    # 创建修复器
    fixer = LabelFormatFixer(args.data_dir, args.backup_dir)
    
    # 分析当前格式
    analysis = fixer.analyze_labels(args.splits)
    
    if args.analyze_only:
        print("📊 仅执行分析，跳过转换步骤")
        return
    
    # 备份原始文件
    if not args.no_backup:
        fixer.backup_labels(args.splits)
    else:
        print("⚠️ 跳过备份步骤")
    
    # 执行转换
    fixer.fix_labels(args.splits)
    
    # 生成并保存报告
    report = fixer.save_report()
    print("\n" + "=" * 50)
    print(report)
    print("🎉 标签格式转换完成！")


if __name__ == '__main__':
    main()