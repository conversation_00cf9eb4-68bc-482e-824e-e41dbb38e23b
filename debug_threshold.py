#!/usr/bin/env python3
"""
调试阈值设置脚本
"""

import os
import sys
import torch
import glob

# 添加路径
sys.path.append('.')
sys.path.append('src')

def load_minimal_model(model_path, device='cuda'):
    """加载极简模型"""
    from src.models.minimal_multimodal import MinimalMultimodalYOLO
    
    model = MinimalMultimodalYOLO(nc=5)
    checkpoint = torch.load(model_path, map_location='cpu', weights_only=False)
    
    if 'model_state_dict' in checkpoint:
        state_dict = checkpoint['model_state_dict']
    else:
        state_dict = checkpoint
    
    model.load_state_dict(state_dict)
    model = model.to(device)
    model.eval()
    
    return model

def load_image_pair(image_path, img_size=640):
    """加载RGB和热红外图像对"""
    try:
        from PIL import Image
        import torchvision.transforms as transforms
        
        rgb_img = Image.open(image_path).convert('RGB')
        thermal_path = image_path.replace('images', 'thermal')
        if thermal_path.endswith('.jpg'):
            thermal_path = thermal_path.replace('.jpg', '.png')
        
        if os.path.exists(thermal_path):
            thermal_img = Image.open(thermal_path).convert('L')
        else:
            thermal_img = rgb_img.convert('L')
        
        transform = transforms.Compose([
            transforms.Resize((img_size, img_size)),
            transforms.ToTensor()
        ])
        
        rgb_tensor = transform(rgb_img).unsqueeze(0)
        thermal_tensor = transform(thermal_img).unsqueeze(0)
        
        return rgb_tensor, thermal_tensor
        
    except Exception as e:
        print(f"图像加载失败，使用随机输入: {e}")
        rgb_tensor = torch.rand(1, 3, img_size, img_size)
        thermal_tensor = torch.rand(1, 1, img_size, img_size)
        return rgb_tensor, thermal_tensor

def get_class_specific_threshold(class_id, base_thresh):
    """为不同类别设置不同的置信度阈值"""
    if class_id == 4:  # insulator
        return max(base_thresh, 0.08)
    elif class_id == 2:  # Glass_Loss
        return max(base_thresh, 0.06)
    elif class_id in [1, 3]:  # Glass_Dirty, Polyme_Dirty
        return max(base_thresh, 0.04)
    else:  # 110_two_hight_glass
        return max(base_thresh, 0.03)

def debug_threshold_effects(model_path, data_dir='data', device='cuda'):
    """调试不同阈值的效果"""
    print("🔍 [DEBUG] 调试阈值效果...")
    
    # 加载模型
    model = load_minimal_model(model_path, device)
    
    # 获取测试图像
    test_images_dir = os.path.join(data_dir, 'test', 'images')
    image_files = glob.glob(os.path.join(test_images_dir, '*.jpg')) + \
                  glob.glob(os.path.join(test_images_dir, '*.png'))
    
    # 使用前3张图像进行测试
    test_files = image_files[:3]
    
    for image_file in test_files:
        image_name = os.path.basename(image_file)
        print(f"\n📊 [IMAGE] {image_name}")
        
        # 加载图像
        rgb_tensor, thermal_tensor = load_image_pair(image_file)
        rgb_tensor = rgb_tensor.to(device)
        thermal_tensor = thermal_tensor.to(device)
        
        # 模型推理
        with torch.no_grad():
            outputs = model(rgb_tensor, thermal_tensor)
            
            if outputs and len(outputs) > 0:
                output = outputs[0]
                batch_size, channels, height, width = output.shape
                
                print(f"  输出形状: {output.shape}")
                
                # 测试不同的阈值
                thresholds = [0.001, 0.01, 0.02, 0.03, 0.05, 0.08, 0.1]
                
                for thresh in thresholds:
                    detection_count = 0
                    class_counts = {0: 0, 1: 0, 2: 0, 3: 0, 4: 0}
                    max_conf_per_class = {0: 0, 1: 0, 2: 0, 3: 0, 4: 0}
                    
                    for h in range(height):
                        for w in range(width):
                            pred = output[0, :, h, w]
                            
                            obj_conf = torch.sigmoid(pred[4])
                            
                            if obj_conf > 0.01:  # 基础过滤
                                class_logits = pred[5:]
                                class_probs = torch.softmax(class_logits, dim=0)
                                class_conf, class_id = class_probs.max(dim=0)
                                
                                final_conf = obj_conf * class_conf
                                class_id_val = class_id.item()
                                
                                # 更新最大置信度
                                max_conf_per_class[class_id_val] = max(
                                    max_conf_per_class[class_id_val], 
                                    final_conf.item()
                                )
                                
                                # 应用类别特定阈值
                                class_thresh = get_class_specific_threshold(class_id_val, thresh)
                                
                                if final_conf > class_thresh:
                                    detection_count += 1
                                    class_counts[class_id_val] += 1
                    
                    print(f"    阈值 {thresh:.3f}: 总检测={detection_count}, 各类别={dict(class_counts)}")
                    print(f"      各类别最大置信度: {dict(max_conf_per_class)}")
                
                # 显示原始置信度分布
                obj_conf_raw = output[0, 4, :, :]
                obj_conf_sigmoid = torch.sigmoid(obj_conf_raw)
                
                print(f"  置信度统计:")
                print(f"    最大: {obj_conf_sigmoid.max():.6f}")
                print(f"    均值: {obj_conf_sigmoid.mean():.6f}")
                print(f"    > 0.1: {(obj_conf_sigmoid > 0.1).sum().item()}")
                print(f"    > 0.05: {(obj_conf_sigmoid > 0.05).sum().item()}")
                print(f"    > 0.01: {(obj_conf_sigmoid > 0.01).sum().item()}")

def main():
    model_path = "runs/train_minimal_curriculum/20250804_132418/weights/best.pt"
    data_dir = "data"
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    print(f"🖥️ [DEVICE] 使用设备: {device}")
    debug_threshold_effects(model_path, data_dir, device)

if __name__ == '__main__':
    main()
