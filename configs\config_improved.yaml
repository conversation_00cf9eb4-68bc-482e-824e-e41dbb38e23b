# 基于性能分析的改进训练配置
# 针对置信度低、类别不平衡等问题的优化方案

# === 核心训练参数 ===
data_dir: "data"
output_dir: "runs/train_improved"
nc: 5  # 删除了无样本的broken disc和pollution-flashover
img_size: 512
batch_size: 6
epochs: 120  # 增加训练轮数
num_workers: 0
grad_clip: 10.0

# === 模型配置 (简化融合策略) ===
model:
  type: "simple"
  fusion_type: "early"  # 使用更简单的融合策略，减少噪声

# === 改进的优化器配置 ===
optimizer:
  type: "Adam"
  lr: 0.0005  # 降低学习率，让置信度分支充分学习
  weight_decay: 0.0001  # 减少权重衰减

# === 学习率调度 (更保守) ===
scheduler:
  type: "CosineAnnealingLR"
  T_max: 120  # 与epochs匹配
  eta_min: 0.00001

# === 改进的早停配置 ===
early_stopping:
  patience: 25    # 增加耐心，允许更多训练
  min_delta: 0.0005  # 降低最小改善阈值

# === 损失函数权重调整 ===
loss_weights:
  box_loss: 1.0      # 边界框损失
  obj_loss: 2.0      # 增加置信度损失权重 (原来可能是1.0)
  cls_loss: 0.5      # 降低分类损失权重 (原来可能是1.0)

# === 高级配置 ===
use_mixed_precision: true
use_ema: true

# === 增强的数据增强 ===
augmentation:
  brightness_contrast: 0.3  # 增加亮度对比度变化
  blur_prob: 0.15           # 增加模糊概率
  flip_prob: 0.5
  color_jitter: 0.2         # 增加颜色抖动
  rotate_prob: 0.1          # 添加旋转
  scale_range: [0.8, 1.2]   # 添加尺度变化

# === 类别平衡策略 ===
class_balancing:
  use_focal_loss: true      # 使用Focal Loss处理类别不平衡
  alpha: 0.25              # Focal Loss alpha参数
  gamma: 2.0               # Focal Loss gamma参数
  
# === 置信度改进策略 ===
confidence_boost:
  warm_up_epochs: 20       # 前20轮使用较低的置信度阈值训练
  target_confidence: 0.3   # 目标置信度范围

# 说明：
# 1. 降低学习率让模型更细致地学习置信度分支
# 2. 增加置信度损失权重，确保模型学会输出合理的置信度
# 3. 使用更简单的融合策略减少噪声
# 4. 增加训练轮数和耐心，确保充分训练
# 5. 使用Focal Loss处理类别不平衡问题