#!/usr/bin/env python3
"""
测试随机模型的评估结果
验证：即使完全随机的模型，指标也不应该全是0
"""

import torch
import numpy as np
from src.models.multimodal_yolo_fixed import FixedMultimodalYOLO
from src.dataset.multimodal_dataset import create_dataloader

def test_random_model():
    print("🎲 测试完全随机模型的评估结果")
    print("=" * 50)
    
    # 创建完全随机的模型（不加载任何权重）
    model = FixedMultimodalYOLO(nc=5, fusion_type='cross_attention')
    model.eval()
    print("✓ 创建了完全随机初始化的模型")
    
    # 创建数据加载器（只取少量数据测试）
    test_loader = create_dataloader('data', 'test', batch_size=1, img_size=640, num_workers=0, shuffle=False)
    
    total_predictions = 0
    high_conf_predictions = 0
    very_high_conf_predictions = 0
    ultra_low_conf_predictions = 0
    
    print("分析随机模型在前3个样本上的表现...\n")
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(test_loader):
            if batch_idx >= 3:  # 只测试前3个
                break
                
            rgb_images = batch['rgb']
            thermal_images = batch['thermal']
            targets = batch['targets']
            filenames = batch['filenames']
            
            # 随机模型推理
            outputs = model(rgb_images, thermal_images)
            
            print(f"📁 样本 {batch_idx}: {filenames[0]}")
            
            batch_total = 0
            batch_high = 0
            batch_very_high = 0
            batch_ultra_low = 0
            
            for scale_idx, output in enumerate(outputs):
                pred = output[0]  # 第一个batch
                
                # 计算置信度
                obj_conf = torch.sigmoid(pred[..., 4])
                cls_conf = torch.sigmoid(pred[..., 5:])
                max_cls_conf, cls_pred = torch.max(cls_conf, dim=-1)
                final_conf = obj_conf * max_cls_conf
                
                # 统计不同置信度区间的预测
                ultra_low_mask = final_conf > 0.001  # 超低阈值
                high_conf_mask = final_conf > 0.01
                very_high_conf_mask = final_conf > 0.05
                
                scale_total = len(pred)
                scale_ultra_low = ultra_low_mask.sum().item()
                scale_high = high_conf_mask.sum().item()
                scale_very_high = very_high_conf_mask.sum().item()
                
                batch_total += scale_total
                batch_ultra_low += scale_ultra_low
                batch_high += scale_high
                batch_very_high += scale_very_high
                
                if scale_idx == 0:  # 只显示第一个尺度的详细信息
                    print(f"  尺度{scale_idx}: {scale_total}个预测")
                    print(f"    > 0.001置信度: {scale_ultra_low}")
                    print(f"    > 0.01置信度: {scale_high}")
                    print(f"    > 0.05置信度: {scale_very_high}")
                    
                    if scale_ultra_low > 0:
                        top_confs = final_conf[ultra_low_mask].topk(min(5, scale_ultra_low)).values
                        print(f"    最高置信度: {top_confs.tolist()}")
            
            total_predictions += batch_total
            ultra_low_conf_predictions += batch_ultra_low
            high_conf_predictions += batch_high
            very_high_conf_predictions += batch_very_high
            
            print(f"  📊 该样本统计:")
            print(f"    总预测: {batch_total}")
            print(f"    > 0.001: {batch_ultra_low}")
            print(f"    > 0.01: {batch_high}")
            print(f"    > 0.05: {batch_very_high}")
            print()
    
    print("📊 随机模型总体统计:")
    print(f"总预测数: {total_predictions}")
    print(f"超低置信度(>0.001): {ultra_low_conf_predictions}")
    print(f"低置信度(>0.01): {high_conf_predictions}")
    print(f"中等置信度(>0.05): {very_high_conf_predictions}")
    
    print(f"\n🎯 理论分析:")
    print(f"• 随机模型应该产生一些随机预测")
    print(f"• 即使置信度很低，也应该有一些预测通过超低阈值")
    print(f"• 这些随机预测中应该有极少数意外匹配真实标签")
    print(f"• 因此评估指标不应该严格为0")
    
    print(f"\n💡 如果评估结果仍为0，说明：")
    print(f"1. 评估代码的后处理逻辑有问题")
    print(f"2. 置信度阈值设置过高")
    print(f"3. IoU计算或匹配逻辑有bug")
    print(f"4. 数据格式转换有错误")
    
    return ultra_low_conf_predictions > 0

if __name__ == "__main__":
    result = test_random_model()
    print(f"\n🔍 结论: 随机模型产生了{'一些' if result else '没有'}低置信度预测") 