#!/usr/bin/env python3
"""
分析类别分布和样本不平衡问题
"""

import os
import glob
from collections import defaultdict
import matplotlib.pyplot as plt
import numpy as np

def analyze_class_distribution():
    """分析各类别的样本分布"""
    print("📊 分析类别分布...")
    
    # 类别名称
    class_names = ['110_two_hight_glass', 'Glass_Dirty', 'Glass_Loss', 'Polyme_Dirty', 'insulator']
    
    # 统计各数据集分割的类别分布
    splits = ['train', 'valid', 'test']
    class_counts = {}
    
    for split in splits:
        labels_dir = f'data/{split}/labels'
        if not os.path.exists(labels_dir):
            print(f"⚠️ {split} 标签目录不存在")
            continue
            
        # 统计每个类别的样本数
        split_counts = defaultdict(int)
        label_files = glob.glob(f'{labels_dir}/*.txt')
        
        for label_file in label_files:
            try:
                with open(label_file, 'r') as f:
                    lines = f.readlines()
                    for line in lines:
                        if line.strip():
                            class_id = int(line.split()[0])
                            if 0 <= class_id < len(class_names):
                                split_counts[class_id] += 1
            except Exception as e:
                print(f"读取标签文件失败: {label_file}, 错误: {e}")
        
        class_counts[split] = split_counts
        
        print(f"\n{split} 数据集类别分布:")
        total_samples = sum(split_counts.values())
        for class_id in range(len(class_names)):
            count = split_counts[class_id]
            percentage = (count / total_samples * 100) if total_samples > 0 else 0
            print(f"  类别{class_id} ({class_names[class_id]}): {count} 样本 ({percentage:.1f}%)")
    
    return class_counts, class_names

def analyze_evaluation_results():
    """分析评估结果"""
    print("\n🎯 分析评估结果...")
    
    # 从评估结果文件读取
    metrics_file = 'runs/eval/metrics.json'
    if os.path.exists(metrics_file):
        import json
        with open(metrics_file, 'r') as f:
            metrics = json.load(f)
        
        print("各类别AP值:")
        ap_per_class = metrics['AP_per_class']
        for class_name, ap in ap_per_class.items():
            print(f"  {class_name}: {ap:.4f}")
        
        print(f"\n整体mAP: {metrics['mAP']:.4f}")
        
        # 分析问题类别
        zero_ap_classes = [name for name, ap in ap_per_class.items() if ap == 0.0]
        low_ap_classes = [name for name, ap in ap_per_class.items() if 0 < ap < 0.1]
        
        print(f"\nAP为0的类别: {zero_ap_classes}")
        print(f"AP很低(<0.1)的类别: {low_ap_classes}")
        
        return metrics
    else:
        print(f"评估结果文件不存在: {metrics_file}")
        return None

def identify_problems(class_counts, metrics):
    """识别问题根源"""
    print("\n🔍 问题根源分析...")
    
    if not metrics:
        print("无法分析，缺少评估结果")
        return
    
    class_names = ['110_two_hight_glass', 'Glass_Dirty', 'Glass_Loss', 'Polyme_Dirty', 'insulator']
    ap_per_class = metrics['AP_per_class']
    
    # 分析样本数量与性能的关系
    train_counts = class_counts.get('train', {})
    test_counts = class_counts.get('test', {})
    
    print("样本数量与性能关系:")
    for i, class_name in enumerate(class_names):
        train_count = train_counts.get(i, 0)
        test_count = test_counts.get(i, 0)
        ap = ap_per_class.get(class_name, 0.0)
        
        print(f"  {class_name}:")
        print(f"    训练样本: {train_count}, 测试样本: {test_count}")
        print(f"    AP: {ap:.4f}")
        
        # 分析问题
        if ap == 0.0:
            if train_count < 50:
                print(f"    ❌ 问题: 训练样本过少 ({train_count})")
            if test_count < 5:
                print(f"    ❌ 问题: 测试样本过少 ({test_count})")
        elif ap < 0.1:
            print(f"    ⚠️ 问题: 性能很低，可能是样本质量或类别不平衡")

def suggest_solutions():
    """提供解决方案建议"""
    print("\n💡 解决方案建议:")
    
    print("=== 立即可尝试的方案 ===")
    print("1. 🎯 调整类别权重")
    print("   - 对样本少的类别增加权重")
    print("   - Glass_Loss: 权重 x3-5")
    print("   - insulator: 权重 x3-5")
    print("   - Polyme_Dirty: 权重 x2")
    
    print("\n2. 📉 降低置信度阈值")
    print("   - 评估时使用更低的置信度阈值 (0.1 或 0.05)")
    print("   - 训练时使用Focal Loss处理类别不平衡")
    
    print("\n3. 🔄 数据增强策略")
    print("   - 对少数类别进行过采样")
    print("   - 增加针对性的数据增强")
    
    print("\n=== 长期改进方案 ===")
    print("1. 📊 数据集改进")
    print("   - 收集更多少数类别样本")
    print("   - 检查标注质量")
    print("   - 平衡各类别分布")
    
    print("\n2. 🏗️ 模型架构优化")
    print("   - 使用专门处理不平衡数据的损失函数")
    print("   - 调整模型架构，增强小目标检测能力")
    print("   - 使用集成学习方法")

def main():
    print("🔍 绝缘子检测模型性能分析")
    print("=" * 50)
    
    # 分析类别分布
    class_counts, class_names = analyze_class_distribution()
    
    # 分析评估结果
    metrics = analyze_evaluation_results()
    
    # 识别问题
    identify_problems(class_counts, metrics)
    
    # 提供解决方案
    suggest_solutions()

if __name__ == '__main__':
    main()
