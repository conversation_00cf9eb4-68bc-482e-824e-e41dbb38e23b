#!/usr/bin/env python3
"""
使用合适的阈值快速验证模型性能
"""

import sys
sys.path.append('src')
sys.path.append('.')

from src.training.evaluate_multimodal import MultimodalEvaluator
import yaml

def quick_evaluation():
    """快速评估测试"""
    print("🚀 快速评估测试")
    
    # 配置
    config = {
        'data_dir': 'data',
        'batch_size': 4,  # 小批次，减少内存使用
        'img_size': 640,
        'num_workers': 0,
        'model': {
            'fusion_type': 'cross_attention'
        }
    }
    
    model_path = 'runs/train_optimized/20250730_131733/weights/best.pt'
    
    print("创建评估器...")
    try:
        evaluator = MultimodalEvaluator(config, model_path)
        print("✅ 评估器创建成功")
    except Exception as e:
        print(f"❌ 评估器创建失败: {e}")
        return None
    
    print("测试不同的置信度阈值...")
    
    thresholds = [0.001, 0.005, 0.01, 0.02]
    results = []
    
    for thresh in thresholds:
        print(f"\n=== 置信度阈值: {thresh} ===")
        try:
            metrics = evaluator.evaluate(conf_thresh=thresh, iou_thresh=0.5)
            
            print(f"mAP: {metrics['mAP']:.4f}")
            print(f"总体精确率: {metrics['overall_precision']:.4f}")
            print(f"总体召回率: {metrics['overall_recall']:.4f}")
            
            results.append((thresh, metrics['mAP'], metrics['overall_precision'], metrics['overall_recall']))
            
            # 如果找到有效结果，记录但继续测试
            if metrics['mAP'] > 0:
                print(f"✅ 在阈值 {thresh} 下找到有效检测！")
                
        except Exception as e:
            print(f"❌ 评估失败: {e}")
            continue
    
    # 找到最佳阈值
    valid_results = [(thresh, mAP, precision, recall) for thresh, mAP, precision, recall in results if mAP > 0]
    
    if valid_results:
        # 选择mAP最高的阈值
        best_thresh, best_mAP, best_precision, best_recall = max(valid_results, key=lambda x: x[1])
        print(f"\n🎯 最佳阈值: {best_thresh}")
        print(f"   mAP: {best_mAP:.4f}")
        print(f"   精确率: {best_precision:.4f}")
        print(f"   召回率: {best_recall:.4f}")
        return best_thresh
    else:
        print("❌ 所有阈值都无法产生有效检测")
        return None

if __name__ == '__main__':
    optimal_thresh = quick_evaluation()
    if optimal_thresh:
        print(f"\n🎯 推荐阈值: {optimal_thresh}")
        print(f"完整评估命令: python -m src.training.evaluate_multimodal --model_path runs/train_optimized/20250730_131733/weights/best.pt --data_dir data --conf_thresh {optimal_thresh} --iou_thresh 0.5")
    else:
        print("\n⚠️ 模型可能需要重新训练")