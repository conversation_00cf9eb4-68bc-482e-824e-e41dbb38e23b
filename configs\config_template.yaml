# 多模态绝缘子检测模型训练配置文件

# 数据配置
data_dir: "data"  # 数据集根目录
output_dir: "runs/train"  # 输出目录
nc: 5  # 类别数量（删除了无样本的broken disc和pollution-flashover）
img_size: 640  # 输入图像尺寸
batch_size: 16  # 批次大小（GPU推荐16，CPU推荐2-4）
epochs: 100  # 训练轮数
num_workers: 4  # 数据加载器工作进程数（Windows系统自动设为0，Linux/macOS可用4-8）
grad_clip: 10.0  # 梯度裁剪阈值

# 模型配置
model:
  type: "simple"  # 模型类型 (simple)
  yolo_model_path: "yolov8n.pt"  # 预训练YOLO模型路径
  fusion_type: "cross_attention"  # 特征融合类型 (cross_attention, spatial_attention, pyramid_fusion)

# 优化器配置
optimizer:
  type: "Adam"  # 优化器类型 (Adam, SGD)
  lr: 0.001  # 学习率
  weight_decay: 0.0001  # 权重衰减

# 学习率调度器配置
scheduler:
  type: "CosineAnnealingLR"  # 调度器类型 (StepLR, CosineAnnealingLR)
  step_size: 30  # StepLR步长（仅在使用StepLR时有效）
  gamma: 0.1  # StepLR衰减因子（仅在使用StepLR时有效）

# 早停配置
early_stopping:
  patience: 10  # 早停容忍轮数

# 训练技巧
use_mixed_precision: false  # 是否使用混合精度训练
use_ema: false  # 是否使用指数移动平均

# 预训练权重或恢复训练
# resume: "runs/train/20231201_120000/weights/last.pt"  # 恢复训练检查点
# pretrained: "path/to/pretrained/weights.pt"  # 预训练权重路径 