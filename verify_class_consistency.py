#!/usr/bin/env python3
"""
验证项目中类别数量的一致性
确保所有配置文件和代码都使用正确的类别数（nc=5）
"""

import os
import yaml
import re
from pathlib import Path

def check_yaml_files():
    """检查YAML配置文件中的nc值"""
    print("🔍 检查YAML配置文件...")
    
    yaml_files = [
        'configs/data.yaml',
        'configs/config_optimized.yaml',
        'configs/config_template.yaml',
        'configs/config_cpu.yaml',
        'configs/config_improved_eval.yaml'
    ]
    
    issues = []
    
    for yaml_file in yaml_files:
        if os.path.exists(yaml_file):
            try:
                with open(yaml_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                
                nc = config.get('nc')
                if nc is None:
                    # 检查model.nc
                    nc = config.get('model', {}).get('nc')
                
                if nc is not None:
                    if nc != 5:
                        issues.append(f"❌ {yaml_file}: nc={nc} (应该是5)")
                    else:
                        print(f"✅ {yaml_file}: nc={nc}")
                else:
                    print(f"⚠️ {yaml_file}: 未找到nc配置")
                    
            except Exception as e:
                print(f"❌ {yaml_file}: 读取失败 - {e}")
        else:
            print(f"⚠️ {yaml_file}: 文件不存在")
    
    return issues

def check_python_files():
    """检查Python文件中的硬编码nc值"""
    print("\n🔍 检查Python文件中的硬编码...")
    
    python_files = [
        'src/models/multimodal_yolo_fixed.py',
        'src/training/train_multimodal.py',
        'src/training/evaluate_multimodal.py',
        'src/inference/inference_multimodal.py',
        'test_random_model.py'
    ]
    
    issues = []
    
    for py_file in python_files:
        if os.path.exists(py_file):
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找nc=数字的模式
            nc_patterns = re.findall(r'nc\s*=\s*(\d+)', content)
            
            for nc_value in nc_patterns:
                if int(nc_value) != 5:
                    issues.append(f"❌ {py_file}: 发现 nc={nc_value} (应该是5)")
            
            if nc_patterns:
                valid_nc = all(int(nc) == 5 for nc in nc_patterns)
                if valid_nc:
                    print(f"✅ {py_file}: 所有nc值都是5")
                else:
                    print(f"❌ {py_file}: 存在错误的nc值")
            else:
                print(f"ℹ️ {py_file}: 未找到硬编码的nc值")
        else:
            print(f"⚠️ {py_file}: 文件不存在")
    
    return issues

def check_class_names():
    """检查类别名称的一致性"""
    print("\n🔍 检查类别名称一致性...")
    
    # 从data.yaml读取标准类别名称
    data_yaml = 'configs/data.yaml'
    if not os.path.exists(data_yaml):
        print(f"❌ {data_yaml} 不存在，无法验证类别名称")
        return []
    
    with open(data_yaml, 'r', encoding='utf-8') as f:
        data_config = yaml.safe_load(f)
    
    standard_names = data_config.get('names', [])
    standard_nc = data_config.get('nc', 0)
    
    print(f"📋 标准配置: nc={standard_nc}, 类别={standard_names}")
    
    issues = []
    
    if len(standard_names) != standard_nc:
        issues.append(f"❌ data.yaml: 类别数量不匹配 (nc={standard_nc}, 实际类别数={len(standard_names)})")
    
    if len(standard_names) != 5:
        issues.append(f"❌ data.yaml: 类别数量应该是5，实际是{len(standard_names)}")
    
    # 检查其他配置文件中的类别名称
    config_files = [
        'configs/config_improved_eval.yaml'
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            class_names = config.get('class_names', [])
            if class_names:
                if class_names != standard_names:
                    issues.append(f"❌ {config_file}: 类别名称不一致")
                    print(f"  标准: {standard_names}")
                    print(f"  实际: {class_names}")
                else:
                    print(f"✅ {config_file}: 类别名称一致")
    
    return issues

def main():
    """主函数"""
    print("🔧 验证项目中类别数量的一致性")
    print("=" * 50)
    
    all_issues = []
    
    # 检查YAML文件
    yaml_issues = check_yaml_files()
    all_issues.extend(yaml_issues)
    
    # 检查Python文件
    python_issues = check_python_files()
    all_issues.extend(python_issues)
    
    # 检查类别名称
    class_name_issues = check_class_names()
    all_issues.extend(class_name_issues)
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 验证结果总结:")
    
    if not all_issues:
        print("🎉 所有检查通过！类别配置完全一致。")
        print("✅ 项目配置正确，可以正常训练和评估。")
    else:
        print(f"❌ 发现 {len(all_issues)} 个问题:")
        for issue in all_issues:
            print(f"  {issue}")
        
        print("\n💡 修复建议:")
        print("1. 确保所有配置文件中的 nc=5")
        print("2. 确保所有Python文件中的硬编码 nc=5")
        print("3. 确保类别名称与 configs/data.yaml 一致")
        print("4. 重新训练模型以确保权重文件匹配")
    
    print("\n📋 当前标准配置:")
    print("  nc: 5")
    print("  类别: ['110_two_hight_glass', 'Glass_Dirty', 'Glass_Loss', 'Polyme_Dirty', 'insulator']")
    
    return len(all_issues) == 0

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
