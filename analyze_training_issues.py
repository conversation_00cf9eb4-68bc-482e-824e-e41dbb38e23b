#!/usr/bin/env python3
"""
分析训练问题，找出性能低下的根本原因
"""

import torch
import json
import matplotlib.pyplot as plt
import numpy as np

def analyze_training_history():
    """分析训练历史"""
    print("📈 分析训练历史...")
    
    history_file = "runs/train_optimized/20250730_131733/training_history.json"
    
    try:
        with open(history_file, 'r') as f:
            history = json.load(f)
        
        train_losses = history['train_losses']
        val_losses = history['val_losses']
        
        print(f"训练轮数: {len(train_losses)}")
        print(f"最终训练损失: {train_losses[-1]:.4f}")
        print(f"最终验证损失: {val_losses[-1]:.4f}")
        print(f"最低验证损失: {min(val_losses):.4f}")
        
        # 分析损失趋势
        print("\n📊 损失趋势分析:")
        
        # 最后10轮的平均变化
        if len(train_losses) >= 10:
            recent_train = train_losses[-10:]
            recent_val = val_losses[-10:]
            
            train_trend = (recent_train[-1] - recent_train[0]) / 10
            val_trend = (recent_val[-1] - recent_val[0]) / 10
            
            print(f"最后10轮训练损失趋势: {train_trend:+.6f}/轮")
            print(f"最后10轮验证损失趋势: {val_trend:+.6f}/轮")
            
            if abs(train_trend) < 0.001 and abs(val_trend) < 0.001:
                print("⚠️ 损失趋势平缓，可能已经收敛")
            elif train_trend < 0 and val_trend > 0:
                print("⚠️ 可能存在过拟合：训练损失下降但验证损失上升")
            elif val_trend < -0.005:
                print("✅ 验证损失仍在下降，模型还在改进")
        
        # 损失范围分析
        final_loss = val_losses[-1]
        if final_loss > 1.0:
            print("❌ 验证损失过高，训练不充分")
        elif final_loss > 0.5:
            print("⚠️ 验证损失偏高，可能需要更多训练")
        else:
            print("✅ 验证损失在合理范围")
            
        return history
        
    except Exception as e:
        print(f"❌ 无法读取训练历史: {e}")
        return None

def analyze_model_confidence_issue():
    """分析模型置信度问题"""
    print("\n🔍 分析模型置信度问题...")
    
    print("根据之前的调试结果:")
    print("- 对象置信度范围: 0.02% - 2.8%")
    print("- 最终置信度范围: 0.01% - 1.19%")
    print("- 正常情况应该: 10% - 90%")
    
    print("\n可能的原因:")
    print("1. 🎯 损失函数权重问题")
    print("   - 置信度损失权重过低")
    print("   - 分类损失权重过高")
    print("   - 边界框损失权重不平衡")
    
    print("2. 🔄 训练策略问题")
    print("   - 学习率过低，置信度分支学习不充分")
    print("   - 训练轮数不够")
    print("   - 正负样本比例不合理")
    
    print("3. 🏗️ 模型架构问题")
    print("   - 多模态融合引入噪声")
    print("   - 检测头设计不合理")
    print("   - 激活函数选择问题")

def analyze_data_quality():
    """分析数据质量"""
    print("\n📋 数据质量分析...")
    
    print("从评估结果看各类别表现:")
    class_performance = {
        "110_two_hight_glass": 13.97,
        "Glass_Dirty": 1.03,
        "Glass_Loss": 0.0,
        "Polyme_Dirty": 0.0,
        "broken disc": 0.0,
        "insulator": 0.54,
        "pollution-flashover": 0.04
    }
    
    working_classes = [(name, ap) for name, ap in class_performance.items() if ap > 1.0]
    poor_classes = [(name, ap) for name, ap in class_performance.items() if ap < 0.1]
    
    print(f"✅ 表现较好的类别 (>1%): {len(working_classes)}")
    for name, ap in working_classes:
        print(f"   {name}: {ap:.2f}%")
    
    print(f"❌ 表现很差的类别 (<0.1%): {len(poor_classes)}")
    for name, ap in poor_classes:
        print(f"   {name}: {ap:.2f}%")
    
    print("\n💡 数据问题推测:")
    if len(poor_classes) > len(working_classes):
        print("- 可能存在严重的类别不平衡")
        print("- 某些类别的标注质量可能有问题")
        print("- 数据增强可能不够充分")

def provide_solutions():
    """提供解决方案"""
    print("\n🔧 解决方案建议:")
    
    print("=== 立即可尝试的方案 ===")
    print("1. 📉 调整损失函数权重")
    print("   - 增加置信度损失权重")
    print("   - 降低分类损失权重")
    print("   - 平衡各个损失组件")
    
    print("2. ⚙️ 调整训练参数")
    print("   - 降低学习率 (0.001 -> 0.0005)")
    print("   - 增加训练轮数 (80 -> 120)")
    print("   - 调整早停耐心 (15 -> 25)")
    
    print("3. 🎯 改进模型架构")
    print("   - 简化多模态融合")
    print("   - 调整检测头")
    print("   - 使用预训练骨干网络")
    
    print("\n=== 长期改进方案 ===")
    print("1. 📊 数据质量改进")
    print("   - 检查标注质量")
    print("   - 平衡类别分布")
    print("   - 增强数据增强")
    
    print("2. 🏗️ 架构重新设计")
    print("   - 使用成熟的YOLO架构")
    print("   - 改进融合策略")
    print("   - 添加注意力机制")

def main():
    print("🔍 训练问题深度分析")
    print("=" * 50)
    
    # 分析训练历史
    history = analyze_training_history()
    
    # 分析置信度问题
    analyze_model_confidence_issue()
    
    # 分析数据质量
    analyze_data_quality()
    
    # 提供解决方案
    provide_solutions()

if __name__ == '__main__':
    main()