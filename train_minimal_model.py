#!/usr/bin/env python3
"""
使用极简模型训练脚本
专门针对小数据集设计的轻量级训练方案

支持三种训练策略：
1. 传统训练 (--mode traditional)
2. 渐进式训练 (--mode progressive)
3. 课程学习训练 (--mode curriculum)
"""

import os
import sys
import yaml
import torch
import torch.nn as nn
import argparse
from datetime import datetime

# 添加路径
sys.path.append('.')
sys.path.append('src')

from src.training.train_multimodal import MultimodalTrainer
from src.models.minimal_multimodal import MinimalMultimodalYOLO

class MinimalTrainer(MultimodalTrainer):
    """极简模型训练器"""

    def __init__(self, config, training_mode='traditional'):
        super().__init__(config)
        self.training_mode = training_mode
        print(f"🚀 [MINIMAL] 使用极简模型训练器 - {training_mode}模式")

        # 类别权重
        self.class_weights = config.get('class_weights', [1.0, 3.0, 10.0, 4.0, 15.0])
        print(f"🎯 [MINIMAL] 类别权重: {self.class_weights}")

        # 强化训练策略
        self.strong_augmentation = config.get('augmentation', {})
        print(f"🔄 [MINIMAL] 使用强化数据增强")

        # 课程学习相关配置
        if training_mode == 'curriculum':
            self._setup_curriculum_learning(config)

    def _setup_curriculum_learning(self, config):
        """设置课程学习参数"""
        print(f"📚 [CURRICULUM] 设置课程学习策略...")

        # 类别样本统计
        self.class_samples = {
            0: 180,  # 110_two_hight_glass
            1: 158,  # Glass_Dirty
            2: 38,   # Glass_Loss
            3: 148,  # Polyme_Dirty
            4: 29    # insulator
        }

        # 根据样本数量定义难度等级
        total_samples = sum(self.class_samples.values())
        self.class_difficulty = {}
        for cls, samples in self.class_samples.items():
            ratio = samples / total_samples
            if ratio > 0.25:
                self.class_difficulty[cls] = 'easy'
            elif ratio > 0.15:
                self.class_difficulty[cls] = 'medium'
            else:
                self.class_difficulty[cls] = 'hard'

        print(f"📊 [CURRICULUM] 类别难度分析:")
        class_names = ['110_two_hight_glass', 'Glass_Dirty', 'Glass_Loss', 'Polyme_Dirty', 'insulator']
        for cls, difficulty in self.class_difficulty.items():
            samples = self.class_samples[cls]
            ratio = samples / total_samples * 100
            print(f"  类别{cls} ({class_names[cls]}): {samples}样本 ({ratio:.1f}%) - {difficulty}")

    def get_curriculum_weights(self, epoch, total_epochs):
        """根据课程学习策略计算当前epoch的类别权重"""
        if self.training_mode != 'curriculum':
            return self.class_weights

        # 课程学习阶段划分
        stage1_end = total_epochs * 0.25  # 前25%: 简单类别为主
        stage2_end = total_epochs * 0.50  # 25%-50%: 逐步引入中等难度
        stage3_end = total_epochs * 0.75  # 50%-75%: 重点训练困难类别
        # 75%-100%: 全类别平衡训练

        base_weights = [1.0, 3.0, 10.0, 4.0, 15.0]  # 基础权重

        if epoch <= stage1_end:
            # 阶段1: 简单类别高权重，困难类别低权重
            stage_weights = [2.0, 1.5, 0.5, 1.0, 0.3]
            print(f"📚 [CURRICULUM] 阶段1 - 简单类别为主")
        elif epoch <= stage2_end:
            # 阶段2: 逐步增加中等难度类别权重
            stage_weights = [1.5, 2.0, 3.0, 2.0, 1.0]
            print(f"📚 [CURRICULUM] 阶段2 - 引入中等难度")
        elif epoch <= stage3_end:
            # 阶段3: 重点训练困难类别
            stage_weights = [1.0, 2.0, 8.0, 3.0, 12.0]
            print(f"📚 [CURRICULUM] 阶段3 - 重点困难类别")
        else:
            # 阶段4: 全类别平衡训练
            stage_weights = base_weights
            print(f"📚 [CURRICULUM] 阶段4 - 全类别平衡")

        return stage_weights
    
    def _create_model(self):
        """创建极简模型"""
        print(f"🏗️ [MINIMAL] 创建极简多模态模型...")
        
        model = MinimalMultimodalYOLO(nc=self.config['nc'])
        model = model.to(self.device)
        
        # 打印模型信息
        total_params = sum(p.numel() for p in model.parameters())
        print(f"📊 [MINIMAL] 模型参数量: {total_params:,}")
        print(f"📊 [MINIMAL] 参数/样本比例: {total_params/553:.1f}")
        
        return model
    
    def train_epoch(self, epoch):
        """训练一个epoch"""
        total_epochs = self.config.get('epochs', 200)
        print(f"\n📚 [MINIMAL EPOCH {epoch}/{total_epochs}] 开始训练...")

        # 根据训练模式调整策略
        if self.training_mode == 'curriculum':
            # 课程学习：动态调整类别权重
            current_weights = self.get_curriculum_weights(epoch, total_epochs)
            self.class_weights = current_weights
            print(f"🎯 [CURRICULUM] 当前权重: {current_weights}")
        elif self.training_mode == 'progressive':
            # 渐进式训练：逐步增强少数类别权重
            if epoch > total_epochs * 0.5:
                enhanced_weights = [w * 1.5 if i in [2, 4] else w for i, w in enumerate(self.class_weights)]
                self.class_weights = enhanced_weights
                print(f"🎯 [PROGRESSIVE] 后期增强权重: {enhanced_weights}")
        else:
            # 传统训练：固定权重
            print(f"🎯 [TRADITIONAL] 固定权重: {self.class_weights}")

        # 调用父类训练方法
        epoch_loss = super().train_epoch(epoch)

        # 每20个epoch打印提醒
        if epoch % 20 == 0:
            print(f"🎯 [PROGRESS] 已完成 {epoch}/{total_epochs} 轮训练")
            print(f"📊 [FOCUS] 重点关注类别: Glass_Loss, insulator")

        return epoch_loss
    
    def evaluate_model(self, dataloader):
        """评估模型，使用极低置信度阈值"""
        print("📊 [MINIMAL] 使用极低置信度阈值评估...")
        
        # 使用多个置信度阈值评估
        conf_thresholds = [0.01, 0.05, 0.1, 0.2]
        
        best_metrics = None
        best_thresh = None
        
        for conf_thresh in conf_thresholds:
            print(f"  测试置信度阈值: {conf_thresh}")
            
            # 这里应该调用评估函数
            # 暂时返回占位符
            metrics = {'mAP': 0.0, 'AP_per_class': {}}
            
            if best_metrics is None or metrics['mAP'] > best_metrics['mAP']:
                best_metrics = metrics
                best_thresh = conf_thresh
        
        print(f"✅ [MINIMAL] 最佳置信度阈值: {best_thresh}")
        return best_metrics

def create_minimal_config():
    """创建极简模型训练配置"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    config = {
        'data_dir': 'data',
        'output_dir': f'runs/train_minimal/{timestamp}',
        'nc': 5,
        'img_size': 640,
        'batch_size': 8,
        'epochs': 200,
        'num_workers': 0,
        'grad_clip': 5.0,
        
        # 极简模型配置
        'model': {
            'type': 'minimal',
            'fusion_type': 'minimal'
        },
        
        # 激进类别权重
        'class_weights': [1.0, 3.0, 10.0, 4.0, 15.0],
        
        'optimizer': {
            'type': 'Adam',
            'lr': 0.001,
            'weight_decay': 0.0001
        },
        
        'scheduler': {
            'type': 'CosineAnnealingLR',
            'T_max': 200,
            'eta_min': 1e-6
        },
        
        'early_stopping': {
            'patience': 40
        },
        
        # Focal Loss
        'loss_config': {
            'use_focal_loss': True,
            'focal_alpha': 0.25,
            'focal_gamma': 2.0
        },
        
        # 损失权重
        'loss_weights': {
            'box_loss': 1.0,
            'obj_loss': 4.0,
            'cls_loss': 3.0
        },
        
        # 强化数据增强
        'augmentation': {
            'brightness_contrast': 0.5,
            'blur_prob': 0.3,
            'flip_prob': 0.5,
            'color_jitter': 0.4,
            'rotate_prob': 0.2,
            'scale_range': [0.6, 1.4],
            'mixup_prob': 0.2,
            'cutmix_prob': 0.2
        },
        
        # 评估配置
        'evaluation': {
            'conf_thresh': 0.01,
            'iou_thresh': 0.5
        }
    }
    
    return config

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='极简多模态模型训练')
    parser.add_argument('--mode', type=str, default='traditional',
                       choices=['traditional', 'progressive', 'curriculum'],
                       help='训练模式: traditional(传统), progressive(渐进式), curriculum(课程学习)')
    parser.add_argument('--config', type=str, default='configs/config_minimal_model.yaml',
                       help='配置文件路径')
    parser.add_argument('--epochs', type=int, default=None,
                       help='训练轮数 (覆盖配置文件)')
    parser.add_argument('--batch_size', type=int, default=None,
                       help='批次大小 (覆盖配置文件)')
    parser.add_argument('--lr', type=float, default=None,
                       help='学习率 (覆盖配置文件)')
    parser.add_argument('--output_dir', type=str, default=None,
                       help='输出目录 (覆盖配置文件)')

    return parser.parse_args()

def main():
    args = parse_args()

    print("🚀 极简模型训练")
    print("=" * 60)
    print(f"🎯 训练模式: {args.mode}")
    print("📊 策略: 极简架构 + 激进权重 + 强化增强 + 长期训练")
    print("=" * 60)
    
    # 检查配置文件
    config_file = args.config
    if os.path.exists(config_file):
        print(f"📄 [CONFIG] 加载配置文件: {config_file}")
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
    else:
        print("📄 [CONFIG] 使用默认极简配置")
        config = create_minimal_config()

    # 命令行参数覆盖配置文件
    if args.epochs is not None:
        config['epochs'] = args.epochs
        print(f"📄 [OVERRIDE] 训练轮数: {args.epochs}")

    if args.batch_size is not None:
        config['batch_size'] = args.batch_size
        print(f"📄 [OVERRIDE] 批次大小: {args.batch_size}")

    if args.lr is not None:
        config['optimizer']['lr'] = args.lr
        print(f"📄 [OVERRIDE] 学习率: {args.lr}")

    if args.output_dir is not None:
        config['output_dir'] = args.output_dir
        print(f"📄 [OVERRIDE] 输出目录: {args.output_dir}")
    else:
        # 根据训练模式设置输出目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        config['output_dir'] = f"runs/train_minimal_{args.mode}/{timestamp}"
    
    print(f"\n🏗️ [MODEL] 极简模型特点:")
    print(f"  参数量: ~10万 (原来的1/500)")
    print(f"  共享主干网络: 提高参数效率")
    print(f"  单尺度检测: 减少计算复杂度")
    print(f"  参数/样本比例: ~180 (健康范围)")
    
    print(f"\n🎯 [STRATEGY] 训练策略:")
    class_weights = config.get('class_weights', [1.0, 3.0, 10.0, 4.0, 15.0])
    class_names = ['110_two_hight_glass', 'Glass_Dirty', 'Glass_Loss', 'Polyme_Dirty', 'insulator']
    
    for i, (name, weight) in enumerate(zip(class_names, class_weights)):
        print(f"  类别{i} ({name}): 权重 x{weight}")
    
    print(f"\n🔄 [AUGMENTATION] 强化数据增强:")
    aug_config = config.get('augmentation', {})
    print(f"  亮度对比度: {aug_config.get('brightness_contrast', 0.5)}")
    print(f"  尺度变化: {aug_config.get('scale_range', [0.6, 1.4])}")
    print(f"  Mixup概率: {aug_config.get('mixup_prob', 0.2)}")
    
    print(f"\n📊 [EVALUATION] 评估策略:")
    eval_config = config.get('evaluation', {})
    print(f"  置信度阈值: {eval_config.get('conf_thresh', 0.01)} (极低)")
    print(f"  训练轮数: {config.get('epochs', 200)} (长期训练)")
    
    # 开始训练
    try:
        trainer = MinimalTrainer(config, training_mode=args.mode)
        print(f"\n🚀 [START] 开始极简模型训练 - {args.mode}模式...")
        trainer.train()
        print(f"\n🎉 [SUCCESS] 训练完成!")
        
        # 输出结果
        output_dir = config['output_dir']
        print(f"\n📁 [OUTPUT] 训练结果:")
        print(f"  模型路径: {output_dir}/weights/best.pt")
        print(f"  日志路径: {output_dir}/logs/")
        
        print(f"\n🔍 [NEXT] 建议下一步:")
        print(f"  1. 使用极低置信度阈值评估: python evaluate.py --conf_thresh 0.01")
        print(f"  2. 对比原模型和极简模型的性能")
        print(f"  3. 如果效果好，可以进一步优化超参数")
        print(f"  4. 考虑集成多个极简模型提升性能")
        
    except Exception as e:
        print(f"❌ [ERROR] 训练失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
