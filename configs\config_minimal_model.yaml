# 极简模型训练配置
# 针对小数据集优化，使用参数量仅10万的极简模型

# === 核心训练参数 ===
data_dir: "data"
output_dir: "runs/train_minimal"
nc: 5
img_size: 640  # 保持标准输入尺寸
batch_size: 8   # 可以适当增加批次大小
epochs: 200     # 增加训练轮数，让小模型充分学习
num_workers: 0
grad_clip: 5.0

# === 模型配置 ===
model:
  type: "minimal"  # 使用极简模型
  fusion_type: "minimal"

# === 激进的类别权重策略 ===
class_weights: [1.0, 3.0, 10.0, 4.0, 15.0]  # 对应5个类别的权重
# 110_two_hight_glass: 1.0 (基准，样本最多)
# Glass_Dirty: 3.0 (中等增强)  
# Glass_Loss: 10.0 (激进增强，样本少)
# Polyme_Dirty: 4.0 (中等增强)
# insulator: 15.0 (最激进增强，样本最少)

# === 优化器配置 ===
optimizer:
  type: "Adam"
  lr: 0.001   # 相对较高的学习率，因为模型简单
  weight_decay: 0.0001  # 减少正则化，因为模型已经很简单

# === 学习率调度 ===
scheduler:
  type: "CosineAnnealingLR"
  T_max: 200
  eta_min: 1e-6

# === 早停配置 ===
early_stopping:
  patience: 40  # 增加耐心，让小模型有更多时间学习
  min_delta: 0.001

# === Focal Loss配置 ===
loss_config:
  use_focal_loss: true
  focal_alpha: 0.25
  focal_gamma: 2.0
  
# === 损失函数权重 ===
loss_weights:
  box_loss: 1.0
  obj_loss: 4.0    # 大幅增加置信度损失权重
  cls_loss: 3.0    # 增加分类损失权重

# === 评估配置 ===
evaluation:
  conf_thresh: 0.01  # 极低的置信度阈值
  iou_thresh: 0.5
  save_best: true

# === 强化数据增强 ===
augmentation:
  brightness_contrast: 0.5  # 增强亮度对比度变化
  blur_prob: 0.3
  flip_prob: 0.5
  color_jitter: 0.4
  rotate_prob: 0.2
  scale_range: [0.6, 1.4]   # 更大的尺度变化
  mixup_prob: 0.2           # 增加mixup增强
  cutmix_prob: 0.2          # 增加cutmix增强
  mosaic_prob: 0.3          # 添加mosaic增强

# === 高级配置 ===
use_mixed_precision: false  # 小模型不需要混合精度
use_ema: true              # 使用指数移动平均

# === 类别特定增强 ===
class_specific_augmentation:
  # 对少数类别进行额外增强
  Glass_Loss:
    extra_augment_prob: 0.9
    copy_paste_prob: 0.5
  insulator:
    extra_augment_prob: 0.9
    copy_paste_prob: 0.5

# === 采样策略 ===
sampling_strategy:
  use_balanced_sampling: true
  oversample_minority: true
  minority_oversample_ratio: 5.0  # 少数类别过采样5倍

# === 正则化策略 ===
regularization:
  dropout_prob: 0.1         # 轻微dropout
  label_smoothing: 0.05     # 标签平滑

# === 学习策略 ===
learning_strategy:
  warmup_epochs: 10         # 预热轮数
  warmup_lr_ratio: 0.1      # 预热学习率比例
  
# === 日志配置 ===
logging:
  log_interval: 5
  save_interval: 20
  class_specific_metrics: true
  
# === 模型特定配置 ===
minimal_model:
  shared_backbone: true     # 使用共享主干网络
  single_scale: true        # 单尺度检测
  fusion_type: "weighted"   # 加权融合

# === 训练策略说明 ===
# 1. 使用极简模型（10万参数）避免过拟合
# 2. 激进的类别权重处理不平衡问题
# 3. 强化数据增强补偿模型容量不足
# 4. 增加训练轮数让小模型充分学习
# 5. 极低置信度阈值捕获弱信号
# 6. 共享主干网络提高参数效率
