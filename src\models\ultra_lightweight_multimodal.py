#!/usr/bin/env python3
"""
超轻量级多模态YOLO模型
专门针对小数据集设计，参数量 < 50万
"""

import torch
import torch.nn as nn
import torch.nn.functional as F

class MicroConvBlock(nn.Module):
    """微型卷积块"""
    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1):
        super().__init__()
        padding = kernel_size // 2
        self.conv = nn.Conv2d(in_channels, out_channels, kernel_size, stride, padding, bias=False)
        self.bn = nn.BatchNorm2d(out_channels)
        self.act = nn.ReLU(inplace=True)
    
    def forward(self, x):
        return self.act(self.bn(self.conv(x)))

class MicroBackbone(nn.Module):
    """微型主干网络，极简设计"""
    
    def __init__(self, in_channels=3):
        super().__init__()
        
        # 极简的特征提取网络
        self.stem = MicroConvBlock(in_channels, 16, 3, 2)  # /2
        
        self.stage1 = MicroConvBlock(16, 32, 3, 2)   # /4
        self.stage2 = MicroConvBlock(32, 64, 3, 2)   # /8
        self.stage3 = MicroConvBlock(64, 128, 3, 2)  # /16
        self.stage4 = MicroConvBlock(128, 256, 3, 2) # /32
    
    def forward(self, x):
        features = []
        
        x = self.stem(x)      # 16 channels
        x = self.stage1(x)    # 32 channels, /4
        
        x = self.stage2(x)    # 64 channels, /8
        features.append(x)    # P3 level
        
        x = self.stage3(x)    # 128 channels, /16
        features.append(x)    # P4 level
        
        x = self.stage4(x)    # 256 channels, /32
        features.append(x)    # P5 level
        
        return features

class MicroFusion(nn.Module):
    """微型特征融合模块"""
    def __init__(self, channels):
        super().__init__()
        # 最简单的融合：直接相加
        self.weight_rgb = nn.Parameter(torch.tensor(0.6))
        self.weight_thermal = nn.Parameter(torch.tensor(0.4))
    
    def forward(self, rgb_feat, thermal_feat):
        # 加权相加融合
        fused = self.weight_rgb * rgb_feat + self.weight_thermal * thermal_feat
        return fused

class MicroDetectionHead(nn.Module):
    """微型检测头"""
    
    def __init__(self, in_channels_list, nc=5, num_anchors=1):  # 减少anchor数量
        super().__init__()
        self.nc = nc
        self.num_anchors = num_anchors
        self.num_outputs = nc + 5  # 类别 + bbox + 置信度
        
        print(f"🔧 [MICRO] 检测头配置: nc={nc}, anchors={num_anchors}, outputs={self.num_outputs}")
        
        # 为每个尺度创建微型检测头
        self.heads = nn.ModuleList()
        for in_channels in in_channels_list:
            head = nn.Sequential(
                # 直接输出，不使用中间层
                nn.Conv2d(in_channels, self.num_anchors * self.num_outputs, 1)
            )
            self.heads.append(head)
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化检测头权重"""
        print("🔧 [MICRO] 初始化检测头权重...")
        
        for head in self.heads:
            output_layer = head[0]  # 只有一层
            
            # 初始化输出层权重
            nn.init.normal_(output_layer.weight, 0, 0.01)
            nn.init.constant_(output_layer.bias, 0)
            
            # 设置置信度偏置为负值
            with torch.no_grad():
                for i in range(self.num_anchors):
                    output_layer.bias[i * self.num_outputs + 4] = -2.0
        
        print("✅ [MICRO] 检测头初始化完成")
    
    def forward(self, features):
        outputs = []
        for i, feature in enumerate(features):
            output = self.heads[i](feature)
            outputs.append(output)
        return outputs

class UltraLightweightMultimodalYOLO(nn.Module):
    """超轻量级多模态YOLO模型
    
    设计目标：
    - 参数量 < 50万
    - 参数/样本比例 < 1000
    - 适合极小数据集训练
    """
    
    def __init__(self, nc=5):
        super().__init__()
        self.nc = nc
        
        print(f"🚀 [MICRO] 创建超轻量级多模态模型: nc={nc}")
        
        # 微型主干网络
        self.rgb_backbone = MicroBackbone(in_channels=3)
        self.thermal_backbone = MicroBackbone(in_channels=1)
        
        # 特征通道数
        self.feature_channels = [64, 128, 256]
        
        # 微型融合模块
        self.fusion_modules = nn.ModuleList([
            MicroFusion(ch) for ch in self.feature_channels
        ])
        
        # 微型检测头（只用1个anchor）
        self.detection_head = MicroDetectionHead(self.feature_channels, nc, num_anchors=1)
        
        # 计算并打印参数量
        self._print_model_info()
    
    def _print_model_info(self):
        """打印模型信息"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        print(f"📊 [MICRO] 模型参数统计:")
        print(f"  总参数量: {total_params:,}")
        print(f"  可训练参数: {trainable_params:,}")
        print(f"  模型大小: {total_params * 4 / 1024 / 1024:.2f} MB")
        
        # 与训练样本的比例
        train_samples = 553
        ratio = total_params / train_samples
        print(f"  参数/样本比例: {ratio:.1f} (目标: <1000)")
        
        if ratio < 100:
            print("  ✅ 参数量很合理，适合小数据集")
        elif ratio < 1000:
            print("  ✅ 参数量合理，适合小数据集")
        else:
            print("  ⚠️ 参数量仍偏高，需要进一步简化")
        
        # 各模块参数量
        print(f"\n📊 [MICRO] 各模块参数量:")
        for name, module in self.named_children():
            module_params = sum(p.numel() for p in module.parameters())
            print(f"  {name}: {module_params:,} ({module_params/total_params*100:.1f}%)")
    
    def forward(self, rgb_input, thermal_input):
        """前向传播"""
        # 提取特征
        rgb_features = self.rgb_backbone(rgb_input)
        thermal_features = self.thermal_backbone(thermal_input)
        
        # 特征融合
        fused_features = []
        for i, (rgb_feat, thermal_feat) in enumerate(zip(rgb_features, thermal_features)):
            fused = self.fusion_modules[i](rgb_feat, thermal_feat)
            fused_features.append(fused)
        
        # 检测头输出
        outputs = self.detection_head(fused_features)
        
        return outputs

# 简化的损失函数
class SimplifiedMultimodalLoss(nn.Module):
    """简化的多模态损失函数"""
    
    def __init__(self, nc=5):
        super().__init__()
        self.nc = nc
        self.bce_loss = nn.BCEWithLogitsLoss()
        self.mse_loss = nn.MSELoss()
    
    def forward(self, predictions, targets):
        """计算损失"""
        total_loss = 0.0
        
        for pred in predictions:
            # 简化的损失计算
            # 这里需要根据实际的标签格式来实现
            # 暂时返回一个占位符损失
            total_loss += pred.mean() * 0.0  # 占位符
        
        return total_loss

if __name__ == '__main__':
    # 添加路径
    import sys
    sys.path.append('.')
    sys.path.append('../..')
    
    # 测试超轻量级模型
    print("🧪 测试超轻量级多模态模型...")
    
    model = UltraLightweightMultimodalYOLO(nc=5)
    
    # 创建测试输入
    rgb_input = torch.randn(1, 3, 640, 640)
    thermal_input = torch.randn(1, 1, 640, 640)
    
    # 前向传播
    with torch.no_grad():
        outputs = model(rgb_input, thermal_input)
    
    print(f"\n📤 输出信息:")
    for i, output in enumerate(outputs):
        print(f"  尺度{i}: {output.shape}")
    
    print("\n✅ 超轻量级模型测试完成！")
    print("\n💡 建议:")
    print("  1. 使用这个超轻量级模型重新训练")
    print("  2. 增加数据增强来补偿模型容量的减少")
    print("  3. 使用更多的训练轮数")
    print("  4. 考虑使用预训练权重进行迁移学习")
