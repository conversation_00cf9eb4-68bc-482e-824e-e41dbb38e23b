#!/usr/bin/env python3
"""
Windows系统专用训练脚本
解决多进程pickle问题和ultralytics冲突问题
"""

import os

# 添加src目录到Python路径
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import sys
import argparse
import yaml
import torch
import torch.nn as nn
import torch.optim as optim
from datetime import datetime
from tqdm import tqdm
import platform

# 确保Windows多进程支持
if platform.system() == 'Windows':
    import multiprocessing
    if hasattr(multiprocessing, 'set_start_method'):
        try:
            multiprocessing.set_start_method('spawn', force=True)
        except RuntimeError:
            pass

# 导入自定义模块
from src.dataset.multimodal_dataset import create_dataloader

# 使用修复版模型
from src.models.multimodal_yolo_fixed import FixedMultimodalYOLO as SimpleMultimodalYOLO, MultimodalLoss
print("✓ 使用修复版多模态YOLO模型")

class WindowsCompatibleTrainer:
    """Windows兼容的训练器"""
    
    def __init__(self, config):
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        # 确保Windows下num_workers=0
        if platform.system() == 'Windows':
            self.config['num_workers'] = 0
            print("🔧 Windows系统自动设置 num_workers=0")
        
        # 创建输出目录
        os.makedirs(config['output_dir'], exist_ok=True)
        
        # 初始化模型
        self.model = self._create_model()
        
        # 初始化优化器
        self.optimizer = self._create_optimizer()
        
        # 初始化损失函数
        self.criterion = MultimodalLoss(nc=config['nc'], device=self.device)
        
        # 创建数据加载器
        self.train_loader = self._create_train_loader()
        self.val_loader = self._create_val_loader()
        
        print(f"✓ 训练器初始化完成")
        print(f"  - 训练样本: {len(self.train_loader.dataset)}")
        print(f"  - 验证样本: {len(self.val_loader.dataset)}")
    
    def _create_model(self):
        """创建模型"""
        model = SimpleMultimodalYOLO(
            nc=self.config['nc'],
            fusion_type=self.config['model'].get('fusion_type', 'cross_attention')
        ).to(self.device)
        
        print(f"✓ 模型创建成功: {self.config['model']['type']}")
        return model
    
    def _create_optimizer(self):
        """创建优化器"""
        if self.config['optimizer']['type'] == 'Adam':
            optimizer = optim.Adam(
                self.model.parameters(),
                lr=self.config['optimizer']['lr'],
                weight_decay=self.config['optimizer']['weight_decay']
            )
        else:
            optimizer = optim.SGD(
                self.model.parameters(),
                lr=self.config['optimizer']['lr'],
                weight_decay=self.config['optimizer']['weight_decay'],
                momentum=0.9
            )
        
        print(f"✓ 优化器创建成功: {self.config['optimizer']['type']}")
        return optimizer
    
    def _create_train_loader(self):
        """创建训练数据加载器"""
        return create_dataloader(
            data_dir=self.config['data_dir'],
            split='train',
            batch_size=self.config['batch_size'],
            img_size=self.config['img_size'],
            num_workers=self.config['num_workers'],  # Windows下为0
            shuffle=True
        )
    
    def _create_val_loader(self):
        """创建验证数据加载器"""
        return create_dataloader(
            data_dir=self.config['data_dir'],
            split='valid',
            batch_size=self.config['batch_size'],
            img_size=self.config['img_size'],
            num_workers=self.config['num_workers'],  # Windows下为0
            shuffle=False
        )
    
    def train_epoch(self, epoch):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        num_batches = 0
        
        pbar = tqdm(self.train_loader, desc=f'Epoch {epoch+1}/{self.config["epochs"]}')
        
        for batch_idx, batch in enumerate(pbar):
            try:
                # 获取数据
                rgb_images = batch['rgb'].to(self.device)
                thermal_images = batch['thermal'].to(self.device)
                targets = batch['targets'].to(self.device)
                
                # 前向传播
                self.optimizer.zero_grad()
                outputs = self.model(rgb_images, thermal_images)
                
                # 计算损失
                loss, loss_items = self.criterion(outputs, targets)
                
                # 反向传播
                loss.backward()
                
                # 梯度裁剪
                if self.config.get('grad_clip', 0) > 0:
                    torch.nn.utils.clip_grad_norm_(
                        self.model.parameters(), 
                        self.config['grad_clip']
                    )
                
                self.optimizer.step()
                
                # 更新统计
                total_loss += loss.item()
                num_batches += 1
                
                # 更新进度条
                pbar.set_postfix({
                    'loss': f'{loss.item():.4f}',
                    'avg_loss': f'{total_loss/num_batches:.4f}'
                })
                
            except Exception as e:
                print(f"批次 {batch_idx} 训练错误: {e}")
                continue
        
        return total_loss / max(num_batches, 1)
    
    def validate_epoch(self, epoch):
        """验证一个epoch"""
        self.model.eval()
        total_loss = 0.0
        num_batches = 0
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(self.val_loader):
                try:
                    # 获取数据
                    rgb_images = batch['rgb'].to(self.device)
                    thermal_images = batch['thermal'].to(self.device)
                    targets = batch['targets'].to(self.device)
                    
                    # 前向传播
                    outputs = self.model(rgb_images, thermal_images)
                    
                    # 计算损失
                    loss, loss_items = self.criterion(outputs, targets)
                    
                    total_loss += loss.item()
                    num_batches += 1
                    
                except Exception as e:
                    print(f"批次 {batch_idx} 验证错误: {e}")
                    continue
        
        return total_loss / max(num_batches, 1)
    
    def save_checkpoint(self, epoch, train_loss, val_loss, is_best=False):
        """保存检查点"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'train_loss': train_loss,
            'val_loss': val_loss,
            'config': self.config
        }
        
        # 保存最新检查点
        checkpoint_path = os.path.join(self.config['output_dir'], 'last.pt')
        torch.save(checkpoint, checkpoint_path)
        
        # 保存最佳检查点
        if is_best:
            best_path = os.path.join(self.config['output_dir'], 'best.pt')
            torch.save(checkpoint, best_path)
            print(f"✓ 保存最佳模型: {best_path}")
    
    def train(self):
        """完整训练流程"""
        print(f"\n🚀 开始训练 ({self.config['epochs']} epochs)")
        print("=" * 60)
        
        best_val_loss = float('inf')
        patience_counter = 0
        patience = self.config.get('early_stopping', {}).get('patience', 10)
        
        for epoch in range(self.config['epochs']):
            try:
                # 训练
                train_loss = self.train_epoch(epoch)
                
                # 验证
                val_loss = self.validate_epoch(epoch)
                
                # 打印结果
                print(f"Epoch {epoch+1}/{self.config['epochs']}:")
                print(f"  Train Loss: {train_loss:.4f}")
                print(f"  Val Loss: {val_loss:.4f}")
                
                # 保存检查点
                is_best = val_loss < best_val_loss
                if is_best:
                    best_val_loss = val_loss
                    patience_counter = 0
                else:
                    patience_counter += 1
                
                self.save_checkpoint(epoch, train_loss, val_loss, is_best)
                
                # 早停检查
                if patience_counter >= patience:
                    print(f"⏹️ 早停触发 (patience={patience})")
                    break
                
            except KeyboardInterrupt:
                print("\n⏹️ 训练被用户中断")
                break
            except Exception as e:
                print(f"❌ Epoch {epoch+1} 出现错误: {e}")
                continue
        
        print("\n🎉 训练完成！")
        print(f"最佳验证损失: {best_val_loss:.4f}")


def create_simple_config():
    """创建简化的Windows兼容配置"""
    return {
        'data_dir': 'data',
        'output_dir': 'runs/train_windows',
        'nc': 7,
        'img_size': 416,  # 使用较小的图像尺寸
        'batch_size': 2,  # 使用较小的批次大小
        'epochs': 20,     # 使用较少的训练轮数
        'num_workers': 0, # Windows下必须为0
        'grad_clip': 10.0,
        
        'model': {
            'type': 'simple',
            'fusion_type': 'cross_attention'
        },
        
        'optimizer': {
            'type': 'Adam',
            'lr': 0.001,
            'weight_decay': 0.0001
        },
        
        'early_stopping': {
            'patience': 5
        }
    }


def main():
    parser = argparse.ArgumentParser(description='Windows兼容的多模态训练')
    parser.add_argument('--config', type=str, help='配置文件路径')
    parser.add_argument('--data_dir', type=str, default='.', help='数据集目录')
    parser.add_argument('--epochs', type=int, default=20, help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=2, help='批次大小')
    
    args = parser.parse_args()
    
    # Windows系统检查
    if platform.system() != 'Windows':
        print("⚠️ 此脚本专为Windows系统设计，其他系统请使用 train_multimodal.py")
    
    # 创建配置
    if args.config and os.path.exists(args.config):
        with open(args.config, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        print(f"✓ 加载配置文件: {args.config}")
    else:
        config = create_simple_config()
        print("✓ 使用默认Windows兼容配置")
    
    # 命令行参数覆盖
    if args.data_dir:
        config['data_dir'] = args.data_dir
    if args.epochs:
        config['epochs'] = args.epochs
    if args.batch_size:
        config['batch_size'] = args.batch_size
    
    # 强制Windows设置
    config['num_workers'] = 0
    
    # 添加时间戳
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    config['output_dir'] = os.path.join(config['output_dir'], f'windows_{timestamp}')
    
    print("\n📋 训练配置:")
    print(f"  数据目录: {config['data_dir']}")
    print(f"  输出目录: {config['output_dir']}")
    print(f"  训练轮数: {config['epochs']}")
    print(f"  批次大小: {config['batch_size']}")
    print(f"  图像尺寸: {config['img_size']}")
    print(f"  工作进程: {config['num_workers']}")
    
    # 开始训练
    try:
        trainer = WindowsCompatibleTrainer(config)
        trainer.train()
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main() 