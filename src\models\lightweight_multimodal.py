#!/usr/bin/env python3
"""
轻量级多模态YOLO模型
专门针对小数据集设计，大幅减少参数量
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math

class LightweightConvBlock(nn.Module):
    """轻量级卷积块"""
    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1, groups=1):
        super().__init__()
        padding = kernel_size // 2
        self.conv = nn.Conv2d(in_channels, out_channels, kernel_size, stride, padding, groups=groups, bias=False)
        self.bn = nn.BatchNorm2d(out_channels)
        self.act = nn.ReLU(inplace=True)
    
    def forward(self, x):
        return self.act(self.bn(self.conv(x)))

class DepthwiseSeparableConv(nn.Module):
    """深度可分离卷积，大幅减少参数量"""
    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1):
        super().__init__()
        padding = kernel_size // 2
        
        # 深度卷积
        self.depthwise = nn.Conv2d(in_channels, in_channels, kernel_size, stride, padding, groups=in_channels, bias=False)
        self.bn1 = nn.BatchNorm2d(in_channels)
        
        # 点卷积
        self.pointwise = nn.Conv2d(in_channels, out_channels, 1, bias=False)
        self.bn2 = nn.BatchNorm2d(out_channels)
        self.act = nn.ReLU(inplace=True)
    
    def forward(self, x):
        x = self.act(self.bn1(self.depthwise(x)))
        x = self.act(self.bn2(self.pointwise(x)))
        return x

class LightweightBackbone(nn.Module):
    """轻量级主干网络，参数量约为原来的1/10"""
    
    def __init__(self, in_channels=3):
        super().__init__()
        
        # 第一层：常规卷积
        self.stem = LightweightConvBlock(in_channels, 32, 3, 2)  # /2
        
        # 后续层：深度可分离卷积
        self.stage1 = nn.Sequential(
            DepthwiseSeparableConv(32, 64, 3, 2),   # /4
            DepthwiseSeparableConv(64, 64, 3, 1),
        )
        
        self.stage2 = nn.Sequential(
            DepthwiseSeparableConv(64, 128, 3, 2),  # /8
            DepthwiseSeparableConv(128, 128, 3, 1),
        )
        
        self.stage3 = nn.Sequential(
            DepthwiseSeparableConv(128, 256, 3, 2), # /16
            DepthwiseSeparableConv(256, 256, 3, 1),
        )
        
        self.stage4 = nn.Sequential(
            DepthwiseSeparableConv(256, 512, 3, 2), # /32
        )
    
    def forward(self, x):
        features = []
        
        x = self.stem(x)      # 32 channels
        x = self.stage1(x)    # 64 channels, /4
        
        x = self.stage2(x)    # 128 channels, /8
        features.append(x)    # P3 level
        
        x = self.stage3(x)    # 256 channels, /16
        features.append(x)    # P4 level
        
        x = self.stage4(x)    # 512 channels, /32
        features.append(x)    # P5 level
        
        return features

class SimpleFusion(nn.Module):
    """简化的特征融合模块"""
    def __init__(self, channels):
        super().__init__()
        # 简单的加权融合
        self.weight_rgb = nn.Parameter(torch.ones(1))
        self.weight_thermal = nn.Parameter(torch.ones(1))
        self.fusion_conv = nn.Conv2d(channels * 2, channels, 1, bias=False)
        self.bn = nn.BatchNorm2d(channels)
        self.act = nn.ReLU(inplace=True)
    
    def forward(self, rgb_feat, thermal_feat):
        # 加权融合
        weighted_rgb = rgb_feat * self.weight_rgb
        weighted_thermal = thermal_feat * self.weight_thermal
        
        # 拼接并降维
        concat_feat = torch.cat([weighted_rgb, weighted_thermal], dim=1)
        fused = self.act(self.bn(self.fusion_conv(concat_feat)))
        
        return fused

class LightweightDetectionHead(nn.Module):
    """轻量级检测头"""
    
    def __init__(self, in_channels_list, nc=5, num_anchors=3):
        super().__init__()
        self.nc = nc
        self.num_anchors = num_anchors
        self.num_outputs = nc + 5  # 类别 + bbox + 置信度
        
        print(f"🔧 [LIGHTWEIGHT] 检测头配置: nc={nc}, anchors={num_anchors}, outputs={self.num_outputs}")
        
        # 为每个尺度创建轻量级检测头
        self.heads = nn.ModuleList()
        for in_channels in in_channels_list:
            head = nn.Sequential(
                # 使用深度可分离卷积减少参数
                DepthwiseSeparableConv(in_channels, 128, 3, 1),
                nn.Conv2d(128, self.num_anchors * self.num_outputs, 1)
            )
            self.heads.append(head)
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化检测头权重"""
        print("🔧 [LIGHTWEIGHT] 初始化检测头权重...")
        
        for head in self.heads:
            # 找到最后一层（输出层）
            output_layer = head[-1]
            
            # 初始化输出层权重
            nn.init.normal_(output_layer.weight, 0, 0.01)
            nn.init.constant_(output_layer.bias, 0)
            
            # 设置置信度偏置为负值，降低初始置信度
            with torch.no_grad():
                # 置信度偏置在每个anchor的第5个位置
                for i in range(self.num_anchors):
                    output_layer.bias[i * self.num_outputs + 4] = -2.0  # sigmoid(-2) ≈ 0.12
        
        print("✅ [LIGHTWEIGHT] 检测头初始化完成")
    
    def forward(self, features):
        outputs = []
        for i, feature in enumerate(features):
            output = self.heads[i](feature)
            outputs.append(output)
        return outputs

class LightweightMultimodalYOLO(nn.Module):
    """轻量级多模态YOLO模型
    
    设计目标：
    - 参数量 < 500万 (原来的1/10)
    - 适合小数据集训练
    - 保持多模态融合能力
    """
    
    def __init__(self, nc=5, fusion_type='simple'):
        super().__init__()
        self.nc = nc
        self.fusion_type = fusion_type
        
        print(f"🚀 [LIGHTWEIGHT] 创建轻量级多模态模型: nc={nc}, fusion={fusion_type}")
        
        # 轻量级主干网络
        self.rgb_backbone = LightweightBackbone(in_channels=3)
        self.thermal_backbone = LightweightBackbone(in_channels=1)
        
        # 特征通道数
        self.feature_channels = [128, 256, 512]
        
        # 简化的融合模块
        self.fusion_modules = nn.ModuleList([
            SimpleFusion(ch) for ch in self.feature_channels
        ])
        
        # 轻量级检测头
        self.detection_head = LightweightDetectionHead(self.feature_channels, nc)
        
        # 计算并打印参数量
        self._print_model_info()
    
    def _print_model_info(self):
        """打印模型信息"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        print(f"📊 [LIGHTWEIGHT] 模型参数统计:")
        print(f"  总参数量: {total_params:,}")
        print(f"  可训练参数: {trainable_params:,}")
        print(f"  模型大小: {total_params * 4 / 1024 / 1024:.2f} MB")
        
        # 与训练样本的比例
        train_samples = 553
        ratio = total_params / train_samples
        print(f"  参数/样本比例: {ratio:.1f} (目标: <100)")
        
        if ratio < 100:
            print("  ✅ 参数量合理，适合小数据集")
        elif ratio < 1000:
            print("  ⚠️ 参数量偏高，需要注意过拟合")
        else:
            print("  ❌ 参数量过高，严重过拟合风险")
    
    def forward(self, rgb_input, thermal_input):
        """前向传播"""
        # 提取特征
        rgb_features = self.rgb_backbone(rgb_input)
        thermal_features = self.thermal_backbone(thermal_input)
        
        # 特征融合
        fused_features = []
        for i, (rgb_feat, thermal_feat) in enumerate(zip(rgb_features, thermal_features)):
            fused = self.fusion_modules[i](rgb_feat, thermal_feat)
            fused_features.append(fused)
        
        # 检测头输出
        outputs = self.detection_head(fused_features)
        
        return outputs

if __name__ == '__main__':
    # 添加路径
    import sys
    import os
    sys.path.append('.')
    sys.path.append('../..')
    # 测试轻量级模型
    print("🧪 测试轻量级多模态模型...")
    
    model = LightweightMultimodalYOLO(nc=5)
    
    # 创建测试输入
    rgb_input = torch.randn(1, 3, 640, 640)
    thermal_input = torch.randn(1, 1, 640, 640)
    
    # 前向传播
    with torch.no_grad():
        outputs = model(rgb_input, thermal_input)
    
    print(f"\n📤 输出信息:")
    for i, output in enumerate(outputs):
        print(f"  尺度{i}: {output.shape}")
    
    print("\n✅ 轻量级模型测试完成！")
